import type { Directive } from 'vue';

const authDirective: Directive = {
  mounted(el, binding) {
    const hasPermission = binding.value;
    if (!hasPermission) {
      el.disabled = true;
      el.classList.add('is-disabled');
    }
  },
  updated(el, binding) {
    const hasPermission = binding.value;
    if (hasPermission) {
      el.disabled = false;
      el.classList.remove('is-disabled');
    } else {
      el.disabled = true;
      el.classList.add('is-disabled');
    }
  },
};

export default authDirective;
