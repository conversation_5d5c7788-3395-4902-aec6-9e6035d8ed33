// stores/aPageState.ts
import { defineStore } from 'pinia';

interface ReturnState {
  fromPage: null | string;
  isDialogOpen: boolean;
  recordId: string;
}

export const usePageReturnState = defineStore('pageReturnState', {
  state: (): ReturnState => ({
    fromPage: '',
    recordId: '',
    isDialogOpen: false,
  }),
  actions: {
    setReturnState(payload: ReturnState) {
      this.fromPage = payload.fromPage;
      this.recordId = payload.recordId;
      this.isDialogOpen = payload.isDialogOpen;
    },
    reset() {
      this.fromPage = '';
      this.recordId = '';
      this.isDialogOpen = false;
    },
  },

  persist: true, // 开启持久化存储
});
