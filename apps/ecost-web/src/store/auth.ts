import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { clearPreferencesCache } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import {
  getAccessCodesApi,
  getUserAllPermissions,
  getUserInfoApi,
  loginApi,
  logoutApi,
} from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore(
  'auth',
  () => {
    const accessStore = useAccessStore();
    const userStore = useUserStore();
    const router = useRouter();
    const userPremissions = ref([]);
    const loginLoading = ref(false);

    /**
     * 异步处理登录操作
     * Asynchronously handle the login process
     * @param params 登录表单数据
     */
    async function authLogin(
      params: Recordable<any>,
      onSuccess?: () => Promise<void> | void,
    ) {
      // 异步处理用户登录操作并获取 accessToken

      let userInfo: null | UserInfo = null;
      try {
        loginLoading.value = true;
        const { accessToken, refreshToken } = await loginApi(params);

        // 如果成功获取到 accessToken
        if (accessToken) {
          // 将 accessToken 存储到 accessStore 中
          accessStore.setAccessToken(accessToken);

          // 将 refreshToken 存储到 refreshToken 中
          accessStore.setRefreshToken(refreshToken);

          // 获取用户信息并存储到 accessStore 中
          const [fetchUserInfoResult, accessCodes] = await Promise.all([
            fetchUserInfo(),
            getAccessCodesApi(),
          ]);

          userInfo = fetchUserInfoResult;

          userStore.setUserInfo(userInfo);
          accessStore.setAccessCodes(accessCodes);
          getUserPremission(); // 获取用户权限
          if (accessStore.loginExpired) {
            accessStore.setLoginExpired(false);
          } else {
            onSuccess
              ? await onSuccess?.()
              : await router.push(userInfo.homePath || DEFAULT_HOME_PATH);
          }

          if (userInfo?.nickname) {
            ElNotification({
              message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.nickname}`,
              title: $t('authentication.loginSuccess'),
              type: 'success',
            });
          }
        }
      } finally {
        loginLoading.value = false;
      }

      return {
        userInfo,
      };
    }

    async function logout(redirect: boolean = false) {
      try {
        await logoutApi();
      } catch {
        // 不做任何处理
      }
      resetAllStores();
      accessStore.setLoginExpired(false);

      // 清除 sessionStorage
      sessionStorage.clear();
      // 清除配置项缓存，用于版本更新后，用户可以重新加载最新配置
      clearPreferencesCache();

      // 回登录页带上当前路由地址
      await router.replace({
        path: LOGIN_PATH,
        query: redirect
          ? {
              redirect: encodeURIComponent(router.currentRoute.value.fullPath),
            }
          : {},
      });
    }

    // 获取用户基本数据
    async function fetchUserInfo() {
      let userInfo: null | UserInfo = null;
      userInfo = await getUserInfoApi();
      userStore.setUserInfo(userInfo);
      return userInfo;
    }
    // 获取用户所有权限数据
    async function getUserPremission() {
      const userId = userStore.userInfo ? userStore.userInfo.id : null;
      if (!userId) {
        console.error('用户ID不存在,无法获取到权限信息');
        return;
      }
      const res = await getUserAllPermissions(userId);

      userPremissions.value = res.modulePermissions;
    }

    function $reset() {
      loginLoading.value = false;
    }

    return {
      $reset,
      authLogin,
      fetchUserInfo,
      getUserPremission,
      userPremissions,
      loginLoading,
      logout,
    };
  },
  {
    persist: true, // 开启持久化存储
  },
);
