import { useRoute } from 'vue-router';

import { useUserStore } from '@vben/stores';

import { useAuthStore } from '#/store';

interface PremissionData {
  actionPermissions: any;
  fieldPermissions: any;
}
export function getCurrentPremission(): PremissionData {
  const route = useRoute();

  // 如果是超级管理员，默认拥有所有权限
  const userSotre = useUserStore();
  if (userSotre.userInfo?.isAdmin) {
    return {
      actionPermissions: {
        apCreate: true, // 新增权限
        apDelete: true, // 删除权限
        apUpdate: true, // 修改权限

        apExport: true, // 导出权限
        apImport: true, // 导入权限
        apPrint: true, // 打印权限
      },
      fieldPermissions: null,
    };
  }

  // 通过模块编码获取当前用户在当前模块的权限信息
  const authSotre = useAuthStore();
  const routeModuleCode = route.meta.moduleCode;
  const allPremissionData = authSotre.userPremissions;
  const currentRoutePremission = allPremissionData.find(
    (v: any) => v.moduleCode === routeModuleCode,
  );
  return (
    currentRoutePremission ?? {
      actionPermissions: {
        apCreate: null, // 新增权限
        apDelete: null, // 删除权限
        apUpdate: null, // 修改权限

        apExport: null, // 导出权限
        apImport: null, // 导入权限
        apPrint: null, // 打印权限
      },
      fieldPermissions: null,
    }
  );
}
