/**
 * 递归查找树节点
 * @param tree
 * @param iterate
 * @param childrenProp
 */
export function findTree(tree: any[], iterate: any, childrenProp = 'children') {
  let result: any = null;

  function traverse(node: any, parent: any, path: any[], index: number) {
    if (result) return; // 如果已经找到结果，停止递归

    const isMatch = iterate(node, parent, path, index);
    if (isMatch) {
      result = {
        item: node,
        parent,
        path: path.concat(node),
        index,
      };
      return;
    }

    if (node[childrenProp] && node[childrenProp].length > 0) {
      node[childrenProp].forEach((child: any, childIndex: number) => {
        if (!result) {
          traverse(child, node, path.concat(node), childIndex);
        }
      });
    }
  }

  tree.forEach((rootNode: any, rootIndex: number) => {
    if (!result) {
      traverse(rootNode, null, [], rootIndex);
    }
  });

  return result;
}

/**
 * 返回指定层级的数据
 * @param treeData
 * @param level
 */
export function getDataByLevelWithPrevious(treeData: any[], level: number) {
  if (level < 1) return []; // 层级从1开始

  let currentLevelIds = new Set();
  let currentLevelData = [];
  const allLevelsData = [];

  // 初始化第一层数据
  const firstLevelData = treeData.filter((item) => item.parentId === null);
  if (level >= 1) {
    allLevelsData.push(...firstLevelData);
    currentLevelIds = new Set(firstLevelData.map((item) => item.id));
    currentLevelData = firstLevelData;
  }

  // 逐层遍历
  for (let i = 1; i < level; i++) {
    if (currentLevelIds.size === 0) break; // 如果当前层没有数据，直接退出

    // 获取下一层的节点
    const nextLevelData = treeData.filter((item) =>
      currentLevelIds.has(item.parentId),
    );
    allLevelsData.push(...nextLevelData);
    currentLevelIds = new Set(nextLevelData.map((item) => item.id));
    currentLevelData = nextLevelData;
  }

  return allLevelsData;
}

/**
 * 本地资源下载
 * @param {string} filePath - 文件的相对路径（相对于项目根目录）
 * @param {string} fileName - 下载后的文件名
 */
export function downloadLocalFile(filePath: string, fileName: string) {
  const link = document.createElement('a');
  link.style.display = 'none';
  document.body.append(link);
  link.download = fileName || 'download';
  link.href = `${window.location.origin}${filePath}`;
  link.click();
  link.remove();
}

/**
 * 线上资源下载
 * @param {string} fileUrl - 文件的相对路径（相对于项目根目录）
 * @param {string} fileName - 下载后的文件名
 */
export async function downloadUrlFile(fileUrl: string, fileName: string) {
  try {
    const response = await fetch(fileUrl, {
      method: 'GET',
    });
    if (!response.ok) throw new Error(`HTTP 错误：${response.status}`);

    const blob = await response.blob();

    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName || 'download';
    document.body.append(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  } catch (error: any) {
    throw new Error(`下载失败：：${error.message}`);
  }
}

/**
 * 判断对象是否为空
 * @param obj
 */
export function isEmptyObject(obj: object) {
  // 检查传入的是否是对象
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  // 检查对象是否有自有属性
  return Object.keys(obj).length === 0;
}

/**
 * 根据 id 返回交换 sort 后的数据
 * @param data
 * @param rowId
 * @param direction
 */
export function swapSortData(
  data: any[],
  rowId: string,
  direction: 'down' | 'up',
) {
  // 查找当前数据
  const row = data.find((item) => item.id === rowId);
  if (!row) return { error: '未找到该数据', updatedData: null };

  // 获取同级数据并按 sort 排序
  const sameLevelData = data
    .filter(
      (item) => item.level === row.level && item.parentId === row.parentId,
    )
    .sort((a, b) => a.sort - b.sort);

  // 查找当前数据的索引
  const currentIndex = sameLevelData.findIndex((item) => item.id === rowId);
  if (currentIndex === -1)
    return { error: '未找到该数据索引', updatedData: null };

  // 计算目标索引
  let targetIndex;
  if (direction === 'up') {
    if (currentIndex === 0) {
      return { error: '已经是同级数据的第一条，无法上移!', updatedData: null };
    }
    targetIndex = currentIndex - 1;
  } else if (direction === 'down') {
    if (currentIndex === sameLevelData.length - 1) {
      return {
        error: '已经是同级数据的最后一条，无法下移!',
        updatedData: null,
      };
    }
    targetIndex = currentIndex + 1;
  } else {
    return { error: '方向参数错误', updatedData: null };
  }

  // 获取受影响的数据
  const affectedData = sameLevelData[targetIndex];

  // 交换 sort 值
  [row.sort, affectedData.sort] = [affectedData.sort, row.sort];

  // 返回交换后的两个数据
  return { error: null, updatedData: [row, affectedData] };
}

/**
 * 根据父级 id 返回改id下所有子级数据，包含父级
 * @param data
 * @param parentId
 * @return list<id>
 */
export function findAllChildren(data: any[], parentId: string): any[] {
  const children: any[] = [];
  function findChildren(parentId: string) {
    // 找到所有直接子项
    const directChildren = data.filter((item) => item.parentId === parentId);
    for (const child of directChildren) {
      children.push(child.id);
      findChildren(child.id); // 递归查找子项的子项
    }
  }

  findChildren(parentId);
  return [...children, parentId];
}

/**
 * 文件流下载 - fileName 需携带后缀
 * @param data
 * @param fileName
 * @param mimeType
 */
export function downloadFile(
  data: any,
  fileName: string,
  mimeType = 'application/octet-stream',
) {
  // 创建一个Blob对象，确保指定正确的MIME类型
  const blob = new Blob([data], { type: mimeType });
  // 创建一个临时的URL
  const url = window.URL.createObjectURL(blob);

  // 创建下载链接
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName; // 设置下载的文件名

  // 触发下载
  document.body.append(a);
  a.click();

  // 清理临时URL
  window.URL.revokeObjectURL(url);
  a.remove();
}
