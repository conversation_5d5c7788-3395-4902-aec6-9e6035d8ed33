import { baseRequestClient, metaServiceRequestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
    refreshToken: string;
  }

  export interface RefreshTokenResult {
    accessToken: string;
    refreshToken: string;
  }
}

/**
 * 预登录
 */
export async function preLoginApi(data: AuthApi.LoginParams) {
  return metaServiceRequestClient.post<AuthApi.LoginResult>(
    '/auth/pre-login',
    data,
  );
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return metaServiceRequestClient.post<AuthApi.LoginResult>(
    '/auth/login',
    data,
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi(
  refreshToken: string,
): Promise<AuthApi.RefreshTokenResult> {
  const res = await baseRequestClient.post('/meta/auth/refresh-token', {
    refreshToken,
  });

  return res.data;
}

/**
 * 退出登录
 */
export async function logoutApi() {
  const res = await baseRequestClient.post('/meta/auth/logout');

  return res.data;
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  // return requestClient.get<string[]>('/auth/codes');
  return [];
}

/**
 * 计算并获取用户的所有权限信息
 */
export async function getUserAllPermissions(userId: string) {
  const params = {
    userId,
  };
  return await metaServiceRequestClient.get('/user/all-permissions', {
    params,
  });
}
