import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取可修改的参数
 *
 */

export const ParamsNamesType = {
  ACCOUNT_CYCLE: 'ACCOUNT_CYCLE', // 账期设置
  MATERIAL_INCOMING_INSPECTION: 'MATERIAL_INCOMING_INSPECTION', // 材料进场验收设置
  MATERIAL_RECEIVING: 'MATERIAL_RECEIVING', // 收料单设置
  BASE_ACCOUNT: 'BASE_ACCOUNT', // 基础单据设置
} as const;

export function getIsEditParams(paramsNames: string) {
  const orgId = sessionStorage.getItem('x-org-id');

  const params = {
    orgId,
    paramsType: 'ECOST',
    paramsNames,
  };

  return metaServiceRequestClient.get(`/org-params`, {
    params,
  });
}
