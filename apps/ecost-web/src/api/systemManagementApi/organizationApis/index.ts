import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取用户组织列表树
 * @param params
 */
export function getUserOrganizationTree(params: any) {
  return metaServiceRequestClient.get('/org/user-org', { params });
}

/**
 * 获取组织数据
 * @param params
 */
export function getOrgOne(params: any) {
  return metaServiceRequestClient.get('/org/one', { params });
}

/**
 * 获取组织列表树
 * @param params
 */
export function getOrganizationTree(params: any) {
  return metaServiceRequestClient.get('/org/list', { params });
}

/**
 * 新增组织
 * @param params
 */
export function addOrg(params: any) {
  return metaServiceRequestClient.post('/org/add', params);
}

/**
 * 修改组织
 * @param params
 */
export function editOrg(id: string, params: any) {
  return metaServiceRequestClient.patch(`/org/update/${id}`, params);
}

/**
 * 修改组织状态
 * @param params
 */
export function boolOrg(id: string, params: any) {
  return metaServiceRequestClient.patch(`/org/bool/${id}`, params);
}
/**
 * 删除组织
 * @param params
 */
export function delOrg(id: string) {
  return metaServiceRequestClient.delete(`/org/del/${id}`);
}
/**
 * 分配组织
 * @param params
 */
export function assetsUserOrg() {}

export default {
  getUserOrganizationTree,
  getOrganizationTree,
  addOrg,
  editOrg,
  boolOrg,
  delOrg,
  assetsUserOrg,
};
