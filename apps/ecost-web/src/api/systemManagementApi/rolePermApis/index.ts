import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取组织列表树
 */
export function roleTree(productCode: string) {
  return metaServiceRequestClient.get('/role/list', {
    params: {
      productCode,
    },
  });
}

/**
 * 获取模块信息
 * @param params
 */
export function roleModel(params: any) {
  return metaServiceRequestClient.get('/role/model', { params });
}

/**
 * 新增角色
 * @param params
 */
export function addRole(params: any) {
  return metaServiceRequestClient.post('/role/add', params);
}

/**
 * 编辑角色
 * @param id 角色id
 * @param params
 */
export function editRole(id: string, params: any) {
  return metaServiceRequestClient.patch(`/role/update/${id}`, params);
}

/**
 * 删除角色
 * @param id 角色id
 */
export function deleteRole(id: string) {
  return metaServiceRequestClient.delete(`/role/del/${id}`);
}

/**
 * 获取用户列表
 * @param params
 */
export function userList(params: any) {
  return metaServiceRequestClient.get(`/user/list`, { params });
}

/**
 * 获取角色信息
 * @param id
 */
export function roleInfo(id: string) {
  return metaServiceRequestClient.get(`/role/info/${id}`);
}

/**
 * 获取用户所有角色
 * @param id
 */
export function userRoleAll(id: string) {
  return metaServiceRequestClient.get(`/user/allRoles/${id}`);
}

/**
 * 获取角色下模块的操作权限
 * @param params
 */
export function roleActionPer(params: any) {
  return metaServiceRequestClient.get('/role/operList', { params });
}

/**
 * 获取角色下模块的字段权限
 * @param params
 */
export function roleFiledPer(params: any) {
  return metaServiceRequestClient.get('/role/fieldList', { params });
}

/**
 * 获取非当前角色下用户
 * @param params
 */
export function filterNotRoleUser(params: any) {
  return metaServiceRequestClient.get('/user/filterUsersByRole', { params });
}

/**
 * 编辑角色操作权限
 * @param data
 */
export function editRoleActionPer(data: any) {
  return metaServiceRequestClient.patch(`/role/update/action/per`, data);
}

/**
 * 编辑角色字段权限
 * @param data
 */
export function editRoleFieldPer(data: any) {
  return metaServiceRequestClient.patch(`/role/update/field/per`, data);
}

/**
 * 获取所有角色列表
 */
export function roleAll() {
  return metaServiceRequestClient.get('/role/roleAll');
}

/**
 * 给角色挂用户
 * @param data
 */
export function roleSetUser(data: any) {
  return metaServiceRequestClient.post('/role/create/roleUser', data);
}

/**
 * 删除用户已有角色
 * @param data
 */
export function delUserRole(data: any) {
  return metaServiceRequestClient.post('/role/delete/userRole', data);
}

/**
 * 获取用户操作权限
 * @param data
 */
export function userPermission(data: any) {
  return metaServiceRequestClient.post('/user/actionPermission', data);
}

/**
 * 更新用户操作权限
 * @param data
 */
export function upDateUserPermission(data: any) {
  return metaServiceRequestClient.patch('/user/update/permission', data);
}

/**
 * 更新用户操作权限
 * @param data
 */
export function authorizePermission(data: any) {
  return metaServiceRequestClient.post('/contract-detail/activateAuth', data);
}

/**
 * 给用户新增角色
 * @param data
 */
export function userUpdateRole(data: any) {
  return metaServiceRequestClient.post('/role/create/roleUser', data);
}

/**
 * 给用户删除角色
 * @param data
 */
export function userdelRole(data: any) {
  return metaServiceRequestClient.post('/role/delete/userRole', data);
}
