import { ecostServiceRequestClient } from '#/api/request';

/**
 * 材料字典 -- 获取版本
 */
export function ListMaterialDictionaryVersion() {
  return ecostServiceRequestClient.get('/material-dictionary-version');
}

/**
 * 材料字典 -- 新增版本
 * @param data 版本数据
 */
export function AddMaterialDictionaryVersion(data: any) {
  return ecostServiceRequestClient.post('/material-dictionary-version', data);
}

/**
 * 材料字典 -- 更新版本
 * @param versionId 版本ID
 * @param data 版本数据
 */
export function UpdateMaterialDictionaryVersion(versionId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-version/${versionId}`,
    data,
  );
}

/**
 * 材料字典 -- 删除版本
 * @param versionId 版本ID
 */
export function DelMaterialDictionaryVersion(versionId: string) {
  return ecostServiceRequestClient.delete(
    `/material-dictionary-version/${versionId}`,
  );
}

/**
 * 材料字典 -- 获取分类列表
 * @param versionId 版本ID
 */
export function ListMaterialDictionaryCategory(versionId: string) {
  return ecostServiceRequestClient.get(`/material-dictionary-category`, {
    params: {
      versionId,
    },
  });
}

/**
 * 材料字典 -- 创建分类
 * @param data 数据对象
 */
export function AddMaterialDictionaryCategory(data: any) {
  return ecostServiceRequestClient.post(`/material-dictionary-category`, data);
}

/**
 * 材料字典 -- 更新分类
 * @param categoryId 分类ID
 * @param data 数据对象
 */
export function UpdateMaterialDictionaryCategory(
  categoryId: string,
  data: any,
) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-category/${categoryId}`,
    data,
  );
}

/**
 * 材料字典 -- 删除分类
 * @param categoryId 分类ID
 */
export function DeleteMaterialDictionaryCategory(categoryId: string) {
  return ecostServiceRequestClient.delete(
    `/material-dictionary-category/${categoryId}`,
  );
}

/**
 * 材料字典 -- 移动分类
 * @param categoryId 分类ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveMaterialDictionaryCategory(
  categoryId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-category/${categoryId}/_move`,
    {},
    {
      moveTo: direction,
    },
  );
}

/**
 * 材料字典 -- 获取明细列表
 * @param params 查询参数
 */
export function ListMaterialDictionaryDetail(params: any) {
  return ecostServiceRequestClient.get(`/material-dictionary-detail`, {
    params,
  });
}

/**
 * 材料字典 -- 创建明细
 * @param data 数据对象
 */
export function AddMaterialDictionaryDetail(data: any) {
  return ecostServiceRequestClient.post(`/material-dictionary-detail`, data);
}

/**
 * 材料字典 -- 更新明细
 * @param detailId 明细ID
 * @param data 数据对象
 */
export function UpdateMaterialDictionaryDetail(detailId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-detail/${detailId}`,
    data,
  );
}

/**
 * 材料字典 -- 删除明细
 * @param detailId 明细ID
 */
export function DeleteMaterialDictionaryDetail(detailId: string) {
  return ecostServiceRequestClient.delete(
    `/material-dictionary-detail/${detailId}`,
  );
}

/**
 * 材料字典 -- 移动明细
 * @param detailId 明细ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveMaterialDictionaryDetail(
  detailId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-detail/${detailId}/_move`,
    {},
    { moveTo: direction },
  );
}

/**
 * 材料字典 -- 获取换算单位列表
 * @param detailId 明细ID
 */
export function ListMaterialDictionaryUnit(detailId: string) {
  return ecostServiceRequestClient.get(`/material-dictionary-unit-convert`, {
    params: {
      materialDictionaryDetailId: detailId,
    },
  });
}

/**
 * 材料字典 -- 创建换算单位
 * @param data 数据对象
 */
export function AddMaterialDictionaryUnit(data: any) {
  return ecostServiceRequestClient.post(
    `/material-dictionary-unit-convert`,
    data,
  );
}

/**
 * 材料字典 -- 更新换算单位
 * @param id 数据id
 * @param data 数据对象
 */
export function UpdateMaterialDictionaryUnit(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-unit-convert/${id}`,
    data,
  );
}

/**
 * 材料字典 -- 删除换算单位
 * @param id 数据id
 */
export function DeleteMaterialDictionaryUnit(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-dictionary-unit-convert/${id}`,
  );
}

/**
 * 材料字典 -- 移动明细
 * @param detailId 明细id
 * @param direction 移动方向 up -上移 down - 下移
 */
export function MoveMaterialDictionaryUnitDetail(
  detailId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/material-dictionary-unit-convert/${detailId}/_move`,
    {},
    {
      moveTo: direction,
    },
  );
}
