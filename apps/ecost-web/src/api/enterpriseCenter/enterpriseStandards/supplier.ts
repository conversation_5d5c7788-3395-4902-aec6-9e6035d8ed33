import { ecostServiceRequestClient } from '#/api/request';

/**
 * 企业供应商名录 -- 列表
 */
export function ListSupplier() {
  return ecostServiceRequestClient.get(
    '/enterprise-standard-supplier-directory/list',
  );
}

/**
 * 企业供应商名录 -- 查询单个供应商
 * @param id - 数据id
 */
export function ByIdSupplier(id: string) {
  return ecostServiceRequestClient.get(
    `/enterprise-standard-supplier-directory/obj/${id}`,
  );
}

/**
 * 企业供应商名录 -- 新增供应商
 * @param data - 供应商数据
 */
export function AddSupplier(data: any) {
  return ecostServiceRequestClient.post(
    '/enterprise-standard-supplier-directory/add',
    data,
  );
}

/**
 * 企业供应商名录 -- 更新供应商
 * @param id - 数据id
 * @param data - 供应商数据
 */
export function UpdateSupplier(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/enterprise-standard-supplier-directory/edit/${id}`,
    data,
  );
}

/**
 * 企业供应商名录 -- 发布供应商
 * @param id - 数据id
 */
export function PublishSupplier(id: string) {
  return ecostServiceRequestClient.patch(
    `/enterprise-standard-supplier-directory/publish/${id}`,
  );
}

/**
 * 企业供应商名录 -- 取消供应商发布
 * @param id - 数据id
 */
export function CancelPublishSupplier(id: string) {
  return ecostServiceRequestClient.patch(
    `/enterprise-standard-supplier-directory/unPublish/${id}`,
  );
}

/**
 * 企业供应商名录 -- 删除供应商
 * @param id - 数据id
 */
export function DeleteSupplier(id: string) {
  return ecostServiceRequestClient.delete(
    `/enterprise-standard-supplier-directory/del/${id}`,
  );
}

/**
 * 企业供应商名录 -- 获取变更记录
 * @param id - 数据id
 */
export function ChangeRecords(id: string) {
  return ecostServiceRequestClient.get(
    `/supplier-directory-change-record/details-list/${id}`,
  );
}

/**
 * 企业供应商名录 -- 获取供应商附件
 * @param id - 供应商数据id
 */
export function FileAllSupplier(id: string) {
  return ecostServiceRequestClient.get(`/supplier-directory-accessory/list`, {
    params: {
      supplierDirectoryId: id,
    },
  });
}

/**
 * 供应商附件上传
 * @param data
 * @returns
 */
export function AddSupplierAccessory(data: any) {
  return ecostServiceRequestClient.post(`/supplier-directory-accessory`, data);
}

/**
 * 供应商附件删除
 * @param params
 * @returns
 */
export function DelSupplierAccessory(id: string) {
  return ecostServiceRequestClient.delete(
    `/supplier-directory-accessory/${id}`,
  );
}
