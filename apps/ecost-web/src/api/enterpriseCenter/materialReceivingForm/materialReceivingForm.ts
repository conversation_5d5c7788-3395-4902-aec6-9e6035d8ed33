import type {
  AuditStatusType,
  PurchaseTypeEnum,
  SubmitStatusType,
} from '#/types/materialManagement';

import { ecostServiceRequestClient } from '#/api/request';

export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED',
  RECEIVED: 'RECEIVED',
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED',
} as const;
export type MaterialReceiptStatusEnum =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL',
  CONCRETE: 'CONCRETE',
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL',
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES',
} as const;
export type MaterialTypeEnmu = (typeof MaterialType)[keyof typeof MaterialType];

export const MaterialSearchType = {
  CONTRACT: 'CONTRACT',
  MATERIAL_DICT: 'MATERIAL_DICT',
} as const;
export type MaterialSearchTypeEnum =
  (typeof MaterialSearchType)[keyof typeof MaterialSearchType];

/**
 * 收料单 -- 获取时间筛选列表
 *
 */
export function getTimeList() {
  return ecostServiceRequestClient.get(`/material-receiving/date-tree`);
}

/**
 * 收料单 -- 获取单据列表
 *
 */
interface getInspectionBillListType {
  year?: number;
  month?: number;
  day?: number;
}
export function getInspectionBillList(params?: getInspectionBillListType) {
  return ecostServiceRequestClient.get(`/material-receiving`, {
    params,
  });
}

/**
 * 收料单 -- 新增进场验收单据
 *
 */
export function addMaterialReceing() {
  return ecostServiceRequestClient.post(`/material-receiving`);
}

/**
 * 收料单 -- 编辑收料单
 *
 */
interface EditMaterialReceingType {
  purchaseType?: PurchaseTypeEnum;
  supplierId?: string;
  supplierName?: string;
  contractId?: string;
  contractName?: string;

  year?: number;
  month?: number;
  day?: number;
}
export function editMaterialReceing(id: string, data: EditMaterialReceingType) {
  return ecostServiceRequestClient.patch(`/material-receiving/${id}`, data);
}

/**
 * 收料单 -- 变更提交状态
 *
 */
interface editInspectionBillType {
  taxExcludedAmount: number; // 不含税金额
  taxIncludedAmount: number; // 含税金额
  submitStatus: SubmitStatusType;
}
export function changeSubmitStatus(id: string, data: editInspectionBillType) {
  return ecostServiceRequestClient.patch(
    `/material-receiving/submit/${id}`,
    data,
  );
}
/**
 * 收料单 -- 变更提交状态
 *
 */

export function changeAuditStatus(id: string, auditStatus: AuditStatusType) {
  const data = {
    auditStatus,
  };

  return ecostServiceRequestClient.patch(
    `/material-receiving/audit/${id}`,
    data,
  );
}

/**
 * 收料单 -- 删除进场验收单据
 *
 */
export function delInspectionBill(id: string) {
  return ecostServiceRequestClient.delete(`/material-receiving/${id}`);
}
/**
 * 收料单 -- 获取供应商和合同列表
 *
 */

interface getSupplierAndContractlListType {
  purchaseType: PurchaseTypeEnum;
}
export function getSupplierAndContractlList(
  params: getSupplierAndContractlListType,
) {
  return ecostServiceRequestClient.get(
    `/material-receiving/supplier-and-contract/list`,
    {
      params,
    },
  );
}

/**
 * 收料单 -- 获取所有合同列表
 *
 */
interface GetContractlListType {
  supplierId?: string;
}
export function getContractlList(params?: GetContractlListType) {
  return ecostServiceRequestClient.get(`/material-receiving/contract-list`, {
    params,
  });
}

/**
 * 收料单 -- 获取所有供应商列表
 *
 */
interface GetSupplierlListType {
  contractId?: string;
}
export function getSupplierlList(params?: GetSupplierlListType) {
  return ecostServiceRequestClient.get(`/material-receiving/supplier-list`, {
    params,
  });
}

/**
 * 收料单 -- 获取可选的材料字典分类【 来源合同的分类 】
 *
 */

interface getMaterialCategoryListType {
  inspectionBillId: string;
  materialSearchType: MaterialSearchTypeEnum;
  purchaseType: string;
}
export function getMaterialCategoryList(params: getMaterialCategoryListType) {
  return ecostServiceRequestClient.get(
    `/material-receiving/${params.inspectionBillId}/detail/material-category/list`,
    {
      params,
    },
  );
}

/**
 * 收料单 -- 获取可选择的材料明细【 来源合同的材料 】
 *
 */
interface getMaterialDetailListType {
  inspectionBillId: string;
  materialSearchType: MaterialSearchTypeEnum;
  materialCategoryId: string;
}
export function getMaterialDetailList(params: getMaterialDetailListType) {
  return ecostServiceRequestClient.get(
    `/material-receiving/${params.inspectionBillId}/detail/material-detail/list`,
    {
      params,
    },
  );
}

/* ----------------------------------------------明细的接口------------------------------------------------------------- */

/**
 * 收料单 -- 查询可选的进场验收单
 *
 */

export function getIncomingBillslList(receivingId: string) {
  return ecostServiceRequestClient.get(
    `/material-receiving-detail/choose/incoming-bills/${receivingId}`,
  );
}

/**
 * 收料单 -- 查询要选择进场验收单明细
 *
 */

export function getIncomingBillsDetailslList(
  incomingInspectionId: string,
  receivingId: string,
) {
  return ecostServiceRequestClient.get(
    `/material-receiving-detail/choose/incoming-details/${incomingInspectionId}/${receivingId}`,
  );
}

/**
 * 收料单 -- 查询收料单明细
 *
 */

export function getInspectionDetailList(receivingId: string) {
  return ecostServiceRequestClient.get(
    `/material-receiving-detail/${receivingId}`,
  );
}

/**
 * 收料单 -- 新增验收单明细
 *
 */
interface addInspectionDetailType {
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
}
export function addInspectionDetail(
  receivingId: string,
  data: addInspectionDetailType[],
) {
  // 额外处理一下数据
  const dataValue = {
    list: data,
  };

  return ecostServiceRequestClient.post(
    `/material-receiving-detail/${receivingId}`,
    dataValue,
  );
}

/**
 * 收料单 -- 编辑验收单明细
 *
 */
interface editInspectionDetail {
  priceExcludingTax: string;
  priceIncludingTax: string;
  taxExcludedAmount: string;
  taxIncludedAmount: string;
  remark: string;
}
export function editInspectionDetail(id: string, data: editInspectionDetail) {
  return ecostServiceRequestClient.patch(
    `/material-receiving-detail/${id}`,
    data,
  );
}

/**
 * 收料单 -- 删除验收单明细
 *
 */
export function delInspectionDetail(id: string) {
  return ecostServiceRequestClient.delete(`/material-receiving-detail/${id}`);
}

/**
 * 收料单 -- 验收单明细上移下移
 *
 */
interface moveInspectionDetailType {
  fromId: string;
  toId: string;
}
export function moveInspectionDetail(params: moveInspectionDetailType) {
  return ecostServiceRequestClient.post(
    `/material-receiving-detail/move`,
    {},
    {
      params,
    },
  );
}

/**
 * 收料单 -- 验收单附件上传
 *
 */
interface AddMaterialReceivingAttachmentType {
  receivingId: string;
  fileName: string;
  fileKey: string;
  fileSize: number;
  fileExt: string;
  fileContentType: string;
}
export function addMaterialReceivingAttachment(
  data: AddMaterialReceivingAttachmentType,
) {
  return ecostServiceRequestClient.post(`/material-receiving-attachment`, data);
}

/**
 * 收料单 -- 获取验收单附件列表
 *
 */

export function getAttachmentList(receivingId: string) {
  return ecostServiceRequestClient.get(
    `/material-receiving-attachment/${receivingId}`,
  );
}

/**
 * 收料单 -- 删除附件
 *
 */
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-receiving-attachment/${id}`,
  );
}

/**
 * 收料单 -- 获取数据追溯记录
 *
 */
export function getRecordList(id: string) {
  return ecostServiceRequestClient.get(
    `/material-receiving-detail/traceability-record/${id}`,
  );
}

/**
 * 收料单 -- 删除数据追溯记录
 *
 */
export function delRecord(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-receiving-detail/traceability-record/${id}`,
  );
}
