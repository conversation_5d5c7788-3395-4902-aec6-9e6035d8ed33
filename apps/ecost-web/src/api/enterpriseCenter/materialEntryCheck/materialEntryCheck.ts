import type {
  AuditStatusType,
  PurchaseTypeEnum,
  SubmitStatusType,
} from '#/types/materialManagement';

import { ecostServiceRequestClient } from '#/api/request';

export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED',
  RECEIVED: 'RECEIVED',
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED',
} as const;
export type MaterialReceiptStatusEnum =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL',
  CONCRETE: 'CONCRETE',
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL',
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES',
} as const;
export type MaterialTypeEnmu = (typeof MaterialType)[keyof typeof MaterialType];

export const MaterialSearchType = {
  CONTRACT: 'CONTRACT',
  MATERIAL_DICT: 'MATERIAL_DICT',
} as const;
export type MaterialSearchTypeEnum =
  (typeof MaterialSearchType)[keyof typeof MaterialSearchType];

/**
 * 材料进场验收单 -- 获取时间筛选列表
 *
 */
export function getTimeList() {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/time-list`,
  );
}

/**
 * 材料进场验收单 -- 获取单据列表
 *
 */
interface getInspectionBillListType {
  year?: number;
  month?: number;
  day?: number;
}
export function getInspectionBillList(params?: getInspectionBillListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/inspection-bill/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 新增进场验收单据
 *
 */
export function addInspectionBill() {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/_add`,
  );
}

/**
 * 材料进场验收单 -- 修改进场验收单据
 *
 */

interface editInspectionBillType {
  id: string;
  purchaseType?: PurchaseTypeEnum;
  supplierId?: string;
  supplierName?: string;
  contractId?: string;
  contractName?: string;
  submitStatus?: SubmitStatusType;
  auditStatus?: AuditStatusType;

  year?: number;
  month?: number;
  day?: number;
}
export function editInspectionBill(data: editInspectionBillType) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/_edit`,
    data,
  );
}

/**
 * 材料进场验收单 -- 删除进场验收单据
 *
 */
export function delInspectionBill(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-incoming-inspection/inspection-bill/${id}`,
  );
}
/**
 * 材料进场验收单 -- 获取供应商和合同列表
 *
 */

interface getSupplierAndContractListType {
  purchaseType: PurchaseTypeEnum;
}
export function getSupplierAndContractList(
  params: getSupplierAndContractListType,
) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/supplier-and-contract/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 获取验收单明细列表
 *
 */

export function getInspectionDetailList(inspectionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${inspectionBillId}/detail/list`,
  );
}

/**
 * 材料进场验收单 -- 获取可选的材料字典分类
 *
 */

interface getMaterialCategoryListType {
  contractId: string;
  materialSearchType: MaterialSearchTypeEnum;
  purchaseType: string;
}
export function getMaterialCategoryList(params: getMaterialCategoryListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/detail/material-category/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 获取可选择的材料明细
 *
 */
interface getMaterialDetailListType {
  contractId: string;
  materialSearchType: MaterialSearchTypeEnum;
  materialCategoryId: string;
}
export function getMaterialDetailList(params: getMaterialDetailListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/detail/material-detail/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 新增验收单明细
 *
 */
interface addInspectionDetailType {
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
}
export function addInspectionDetail(
  inspectionBillId: string,
  data: addInspectionDetailType[],
) {
  // 额外处理一下数据
  const dataValue = {
    data,
  };

  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/${inspectionBillId}/detail/_add`,
    dataValue,
  );
}

/**
 * 材料进场验收单 -- 删除验收单明细
 *
 */
export function delInspectionDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-incoming-inspection/inspection-detail/${id}`,
  );
}

/**
 * 材料进场验收单 -- 编辑验收单明细
 *
 */
interface editInspectionDetail {
  id: string;
  qualityStandard: string;
  unit: string;
  siteEntryQuantity: number;
  actualQuantity: number;
  appearanceDescription: string;
  orderNo: number;
  remark: string;
}
export function editInspectionDetail(data: editInspectionDetail) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-detail/_edit`,
    data,
  );
}

/**
 * 材料进场验收单 -- 验收单明细上移下移
 *
 */
interface moveInspectionDetailType {
  fromId: string;
  toId: string;
}
export function moveInspectionDetail(params: moveInspectionDetailType) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-detail/_move`,
    {},
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 验收单附件上传
 *
 */
interface AddInspectionBillAttachmentType {
  incomingInspectionId: string;
  fileName: string;
  fileKey: string;
  fileSize: number;
  fileExt: string;
  fileContentType: string;
}
export function addInspectionBillAttachment(
  data: AddInspectionBillAttachmentType,
) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/attachment`,
    data,
  );
}

/**
 * 材料进场验收单 -- 获取验收单附件列表
 *
 */

export function getAttachmentList(inspectionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${inspectionBillId}/attachment/list`,
  );
}

/**
 * 材料进场验收单 -- 验收单附件删除
 *
 */
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-incoming-inspection/inspection-bill/attachment/${id}`,
  );
}

export function importExcelData(data: any) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/excel/import`,
    data,
    { timeout: 10 * 60 * 1000 },
  );
}

export function getUnitSelectionData(
  contractId: null | string,
  materialIds: string[],
) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/excel/unit-selection/_query`,
    { materialIds, contractId },
  );
}

export function bulkSaveData(data: any[]) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/excel/save/_bulk`,
    data,
  );
}
