import {
  ecostServiceRequestClient,
  metaServiceRequestClient,
} from '#/api/request';

/**
 * 获取项目参数设置
 * @param params
 */
export function getOrgParams(params: any) {
  return metaServiceRequestClient.get('/org-params', {
    params,
  });
}

export function updateOrgParams(data: any) {
  return metaServiceRequestClient.post('/org-params', data);
}

/**
 * 当前项目下是否有基础单据
 */
export function getOrgHasBaseAccount() {
  return ecostServiceRequestClient.get('/org-params/has-base-account');
}
