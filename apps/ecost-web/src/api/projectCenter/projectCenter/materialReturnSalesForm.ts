import { ecostServiceRequestClient } from '#/api/request';

// 退货单左侧树
export function getTimeList() {
  return ecostServiceRequestClient.get('/material-return-sales-form/date-tree');
}

// 获取收料单下所有的合同列表
export function getContractList(supplierId?: string) {
  return ecostServiceRequestClient.get(
    '/material-return-sales-form/contract-list',
    {
      params: {
        supplierId,
      },
    },
  );
}

// 获取收料单下所有的供应商列表
export function getSupplierList(contractId?: string) {
  return ecostServiceRequestClient.get(
    '/material-return-sales-form/supplier-list',
    {
      params: {
        contractId,
      },
    },
  );
}

// 获取收料单列表
export function getMaterialReturnSalesFormList(params: any) {
  return ecostServiceRequestClient.get('/material-return-sales-form', {
    params,
  });
}

// 新增退货单
export function addMaterialReturnSalesForm() {
  return ecostServiceRequestClient.post('/material-return-sales-form');
}

// 编辑退货单
export function editMaterialReturnSalesForm(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/material-return-sales-form/${id}`,
    data,
  );
}

// 删除退货单
export function deleteMaterialReturnSalesForm(id: string) {
  return ecostServiceRequestClient.delete(`/material-return-sales-form/${id}`);
}

// 提交状态变更
export function changeMaterialReturnSalesFormSubmitStatus(
  id: string,
  data: any,
) {
  return ecostServiceRequestClient.patch(
    `/material-return-sales-form/submit/${id}`,
    data,
  );
}

// 审核状态变更
export function changeAuditStatus(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/material-return-sales-form/audit/${id}`,
    data,
  );
}

// 查询可选的材料分类
export function getMaterialCategoryList(returnSalesFormId: string) {
  return ecostServiceRequestClient.get(
    `/material-return-sales-form-detail/choose/materialCategory/${returnSalesFormId}`,
  );
}

// 查询可选的材料明细
export function getMaterialDetailList(
  returnSalesFormId: string,
  categoryId?: string,
) {
  return ecostServiceRequestClient.get(
    `/material-return-sales-form-detail/choose/materialDetails`,
    {
      params: {
        returnSalesFormId,
        categoryId,
      },
    },
  );
}

// 材料明细保存
export function saveMaterialReturnSalesFormMaterialDetail(data: any) {
  return ecostServiceRequestClient.post(
    '/material-return-sales-form-detail',
    data,
  );
}

// 修改退货数量
export function editMaterialReturnSalesFormMaterialDetail(
  id: string,
  data: any,
) {
  return ecostServiceRequestClient.patch(
    `/material-return-sales-form-detail/${id}`,
    data,
  );
}

// 删除父级
export function deleteMaterialReturnSalesFormMaterialDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-return-sales-form-detail/${id}`,
  );
}

// 查询退货单明细
export function getMaterialReturnSalesFormMaterialDetail(id: string) {
  return ecostServiceRequestClient.get(
    `/material-return-sales-form-detail/${id}`,
  );
}

// 退货单材料上下移
export function changeMaterialReturnSalesFormMaterialDetailSort(
  fromId: string,
  toId: string,
) {
  const data = {
    fromId,
    toId,
  };
  return ecostServiceRequestClient.post(
    `/material-return-sales-form-detail/move`,
    data,
  );
}

// 获取附件列表
export function getAttachmentList(returnSalesFormId: string) {
  return ecostServiceRequestClient.get(
    `/material-return-sales-form-attachment/${returnSalesFormId}`,
  );
}

// 添加附件
export function addAttachment(data: any) {
  return ecostServiceRequestClient.post(
    '/material-return-sales-form-attachment',
    data,
  );
}

// 删除附件
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-return-sales-form-attachment/${id}`,
  );
}
