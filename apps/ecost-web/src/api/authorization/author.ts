import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取租户列表
 */
export function tenantList() {
  return metaServiceRequestClient.get('/tenant/list');
}

/**
 * 新增子租户
 * @param data
 */
export function createNewTenant(data: any) {
  return metaServiceRequestClient.post('/tenant/add', data);
}

/**
 * 编辑租户
 * @param id
 * @param data
 */
export function updateTenant(id: string, data: any) {
  return metaServiceRequestClient.patch(`/tenant/edit${id}`, data);
}

/**
 * 删除子租户
 * @param id
 */
export function deleteSubTenant(id: string) {
  return metaServiceRequestClient.delete(`/tenant/del${id}`);
}

/**
 * 新增客户
 * @param data
 */
export function createNewCustomer(data: any) {
  return metaServiceRequestClient.post('/customer/add', data);
}

/**
 * 编辑客户
 * @param id
 * @param data
 */
export function updateNewCustomer(id: string, data: any) {
  return metaServiceRequestClient.patch(`/customer/edit${id}`, data);
}

/**
 * 删除客户
 * @param id - 客户id
 */
export function deleteCustomer(id: string) {
  return metaServiceRequestClient.delete(`/customer/del${id}`);
}

/**
 * 新增授权单
 */
export function addAuthorReceipt(data: any) {
  return metaServiceRequestClient.post('/contract/add', data);
}

/**
 * 编辑授权单
 * @param id
 * @param data
 */
export function updateAuthorReceipt(id: string, data: any) {
  return metaServiceRequestClient.patch(`/contract/edit${id}`, data);
}

/**
 * 根据授权单id,获取授权单明细列表
 * @param contractId - 授权单id
 */
export function authorReceiptDetails(contractId: string) {
  return metaServiceRequestClient.get(`/contract-detail/list${contractId}`);
}

/**
 * 获取某个客户下的授权单
 * @param customerId - 客户id
 */
export function authorReceiptList(customerId: string) {
  return metaServiceRequestClient.get('/contract/list', {
    params: { customerId },
  });
}

/**
 * 确认生效
 * @param contractId - 授权单id
 */
export function confirMeffectiveness(contractId: string) {
  return metaServiceRequestClient.post(`/contract-detail/confirm${contractId}`);
}

/**
 * 生成激活码
 * @param contractId - 授权单id
 */
export function generateActivationCode(contractId: string) {
  return metaServiceRequestClient.post(
    `/contract-detail/addActivate${contractId}`,
  );
}

/**
 * 导出激活码
 * @param contractId - 授权单id
 */
export function activationCodeExport(contractId: string) {
  return metaServiceRequestClient.post(
    `/contract-detail/export${contractId}`,
    null,
    {
      responseType: 'blob',
    },
  );
}

/**
 * 根据授权单id获取计划码列表
 * @param contractId - 授权单id
 */
export function contractIdByActivationCodeList(contractId: string) {
  return metaServiceRequestClient.get(
    `/contract-detail/getActivateList${contractId}`,
  );
}

/**
 * 修改授权单详情
 * @param id - 授权单详情id
 */
export function updateAuthorReceiptd(id: string, data: any) {
  return metaServiceRequestClient.patch(`/contract-detail/edit${id}`, data);
}

/**
 * 删除授权单详情
 * @param id - 授权单详情id
 */
export function deleteAuthorReceiptDetails(id: string) {
  return metaServiceRequestClient.delete(`/contract-detail/del${id}`);
}

/**
 * 删除授权单
 * @param id - 授权单id
 */
export function deleteAuthorReceipt(id: string) {
  return metaServiceRequestClient.delete(`/contract/del${id}`);
}

/**
 * 获取组织激活码信息
 * @param
 */
export function getActiveCodeByOrg(id: string) {
  return metaServiceRequestClient.get(
    `/contract-detail/getActivateCodeByOrgId${id}`,
  );
}

/**
 * 激活激活码
 * @param
 */
export function activeActiveCode(params: any) {
  return metaServiceRequestClient.post(`/contract-detail/activate`, params);
}

/**
 * 消耗激活码
 * @param
 */
export function consumAciveCode(params: any) {
  return metaServiceRequestClient.post(`/contract-detail/activateAuth`, params);
}

// 修改密码
export function UpdatePassword(params: any) {
  return metaServiceRequestClient.post(`/auth/change-password`, params);
}

// 忘记密码-发送短信验证码
export function sendCode(params: any) {
  return metaServiceRequestClient.post(
    `/auth/forgot-password/send-code`,
    params,
  );
}

// 忘记密码-重置密码
export function resetPassword(params: any) {
  return metaServiceRequestClient.post(`/auth/forgot-password/reset`, params);
}
