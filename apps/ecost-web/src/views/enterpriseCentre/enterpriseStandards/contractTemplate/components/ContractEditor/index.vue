<template>
  <ElDrawer
    v-bind="$attrs"
    v-model="drawerVisible"
    :modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :size="sizeNum"
    modal-class="pointer-events-none"
    class="pointer-events-auto"
    :with-header="false"
    @close="handleClose"
    @opened="handleOpen"
  >
    <div v-loading="contentLoading">
      <div class="header box-border w-full pl-4 pr-4">
        <div class="flex w-full items-center justify-between">
          <div class="layout-header flex h-[60px] items-center justify-between">
            <div class="layout-header-left flex items-center gap-8">
              <div class="flex items-center gap-4">
                <div class="flex self-center">
                  <div class="mr-[4px] mt-[-4px] text-red-600">*</div>
                  <div class="text-sm text-black">合同范本名称</div>
                </div>
                <ElInput
                  size="default"
                  v-model="contractTemplateName"
                  style="width: 240px"
                  placeholder=""
                  :disabled="!editableValue"
                />
              </div>
              <div class="flex items-center gap-4">
                <div class="self-center text-sm text-black">范本说明</div>
                <ElInput
                  size="default"
                  v-model="contractTemplateDesc"
                  style="width: 240px"
                  placeholder=""
                  :disabled="!editableValue"
                />
              </div>
            </div>
          </div>
          <div class="flex">
            <ElButton
              class="mr-5"
              type="primary"
              size="small"
              @click="insureSubmit"
              :disabled="!editableValue"
            >
              保存
            </ElButton>
            <ElTooltip
              :content="isFullScreen ? '收起' : '全屏'"
              placement="top"
            >
              <IconifyIcon
                @click="isFullScreen = !isFullScreen"
                class="icon-box mr-5 text-2xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                :icon="
                  isFullScreen
                    ? 'majesticons:arrows-collapse-full'
                    : 'majesticons:arrows-expand-full-line'
                "
              />
            </ElTooltip>
            <IconifyIcon
              @click="closeClick"
              class="icon-box mr-5 text-2xl outline-none hover:cursor-pointer hover:text-[#006be6]"
              icon="ep:close"
            />
          </div>
        </div>
      </div>
      <div
        class="content flex w-full bg-[#fafafa]"
        style="height: calc(100vh - 70px)"
      >
        <!-- 左边区域 -->
        <div class="flex w-[70%] flex-col">
          <!-- 中间区域 -->
          <div class="flex flex-1 overflow-hidden bg-[#fafafa]">
            <div class="w-full" ref="wpsRef"></div>
          </div>
        </div>
        <!-- 右侧区域 -->
        <div class="box-border-box min-width-[540px] flex w-[30%] flex-col p-4">
          <div
            class="search-container item-center box-border-box mb-4 flex h-[32px] w-full justify-between"
          >
            <div class="btn-group flex items-center">
              <ElButton
                type="primary"
                size="small"
                link
                @click="expand('open')"
              >
                <div class="text-sm">展开</div>
              </ElButton>
              <ElButton
                type="primary"
                size="small"
                link
                @click="expand('close')"
              >
                <div class="text-sm">折叠</div>
              </ElButton>
            </div>
            <div class="w-full pl-8">
              <ElInput
                v-model="searchValue"
                placeholder=""
                :suffix-icon="Search"
                @keyup.enter="searchFiledRule"
                @blur="searchFiledRule"
              />
            </div>
          </div>
          <div class="filed-table-container mb-2 w-full flex-1">
            <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
              <template #top></template>
              <template #name="{ row }">
                <div v-if="row.parentId == null">{{ row.name }}</div>
                <div v-else class="flex justify-center">
                  <div :class="row.isExit ? 'text-green-500' : ''">
                    {{ row.name }}
                  </div>
                  <ElButton
                    v-if="row.parentId !== null && row.children !== null"
                    type="primary"
                    link
                    size="large"
                    :icon="CirclePlus"
                    @click="setFiledClick(row)"
                    :disabled="!editableValue"
                  />
                </div>
              </template>
              <template #isRequired="{ row }">
                <div v-if="row.parentId !== null">
                  <ElCheckbox
                    v-model="row.isRequired"
                    :disabled="!editableValue || row.isDefaultRequired"
                    @change="isRequiredChange(row)"
                  />
                </div>
              </template>
              <template #fieldType="{ row }">
                {{ getFileTypeLabel(row.fieldType) }}
              </template>
              <template #enumValue="{ row }">
                <div v-if="row.parentId !== null">
                  <div v-if="row.fieldType === 'ENUM'">
                    {{ row.enumValue }}
                  </div>
                </div>
              </template>
            </VxeGrid>
          </div>
        </div>
      </div>
    </div>
  </ElDrawer>
</template>

<script lang="ts" setup>
import { computed, nextTick, reactive, ref, watch } from 'vue';

import { useAppConfig } from '@vben/hooks';
import { IconifyIcon } from '@vben/icons';
import { useAccessStore } from '@vben/stores';

import { CirclePlus, Search } from '@element-plus/icons-vue';
import {
  ElButton,
  ElCheckbox,
  ElDrawer,
  ElInput,
  ElMessage,
  ElTooltip,
} from 'element-plus';
import _ from 'lodash';

import {
  addContractStandardFieldRule,
  delContractStandardFieldRule,
  getStandardFieldRuleList,
} from '#/api/enterpriseCenter/contractTemplate/contractTemplate';

interface SubmitPayload {
  name: string;
  remark: string;
  fileId: string;
}

defineOptions({
  name: 'ContractTemplateEditor',
});

const props = withDefaults(
  defineProps<{
    editable: boolean;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'submit', payload: SubmitPayload): void;
  (e: 'deleteMandatory', payload: any): void;
  (e: 'setFiled', payload: any): void;
  (e: 'searchFiled', payload: any): void;
}>();

const getFileTypeLabel = (type: string) => {
  const statusMap: any = {
    TEXT: '文本',
    TABLE: '表格',
    PERCENT: '百分比',
    DATE: '日期',
    ENUM: '枚举值',
    NUMBER: '数字',
  };
  return statusMap[type] || '';
};

const wpsRef = ref(); // wps El
const contentLoading = ref(true);
let instance: any = null; // wps Instance
const drawerVisible = ref(false); // 弹窗是否展示
const isFullScreen = ref(false); // 是否全屏
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '74%';
});
const tableRef = ref(); // 表格Dom
// const mandatoryTerm = ref([]); // 强条数据
const contractTemplateName = ref(''); // 合同范本名称
const contractTemplateDesc = ref(''); // 合同范本说明
const formData = ref({
  id: null,
  classify: '',
  name: '',
  remark: '',

  fileId: '',
});
const searchValue = ref(''); // 查询数据
const editableValue = ref(true);
// 是否展示弹窗
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
// 是否可编辑
watch(
  () => props.editable,
  (nval) => {
    editableValue.value = nval;
  },
);
// 表格数据
const currentItem = ref();
const columns = [
  {
    field: 'name',
    title: '字段名称',
    width: '220',
    // minWidth: '180',
    slots: {
      default: 'name',
    },
    treeNode: true,
  },

  {
    field: 'isRequired',
    title: '必填',
    width: '48',
    slots: {
      default: 'isRequired',
    },
  },
  {
    field: 'fieldType',
    title: '字段类型',
    width: '74',
    slots: {
      default: 'fieldType',
    },
  },
  {
    field: 'enumValue',
    title: '枚举值',
    // width: '140',
    // width:'140',
    slots: {
      default: 'enumValue',
    },
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '98%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  rowClassName: ({ row }: { row: { parentId: null | string } }) => {
    return row.parentId ? '' : 'bg-gray-100';
  },
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  mouseConfig: {
    selected: true,
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  columns,
  data: [],
});
const tableEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
  },
  cellDblclick({ row }: any) {
    if (editableValue.value && row.parentId) {
      setFiledClick(row);
    }
  },
};

// 展开 / 关闭
const expand = (type: string) => {
  if (type === 'close') {
    tableRef.value.clearTreeExpand();
  } else {
    tableRef.value.setAllTreeExpand(true);
  }
};

const defaultValue = '________________';
// 设置字段规则
let tagIdx = 1;
// 生成随机字母+数字
function getRandomLetterAndNumber() {
  const letter = String.fromCodePoint(65 + Math.floor(Math.random() * 26)); // A-Z
  const number = Math.floor(Math.random() * 10); // 0-9
  return letter + number;
}
async function setFiledClick(row: any) {
  await instance.ready();
  const app = instance.Application;

  // 插入控件
  const contentControls = await app.ActiveDocument.ContentControls;
  const contentControl = await contentControls.Add({ Type: 0 });
  contentControl.Tag = row.name;
  contentControl.Title = row.name;

  // 添加书签
  const bookmarks = await app.ActiveDocument.Bookmarks;
  const start = await contentControl.Range.Start;
  const end = await contentControl.Range.End;
  const markNum = getRandomLetterAndNumber();
  const tagName = `${row.name}${tagIdx}${markNum}`;

  await bookmarks.Add({
    Name: tagName,
    Range: {
      Start: start,
      End: end,
    },
  });

  // 设置书签的默认文本
  await bookmarks.ReplaceBookmark([
    {
      type: 'text',
      name: tagName,
      value: defaultValue,
    },
  ]);
  tagIdx++;
  // 调用新增字段接口
  const ID = await contentControl.ID;
  if (!row.isExit) {
    if (!formData.value.id) {
      ElMessage.error('无效的操作,合同范本ID不合法');
      return;
    }
    addFiled(row, String(ID));
  }
}

// 必填字段改变
function isRequiredChange(row: any) {
  if (row.isExit) {
    // 因为change后会直接改变isRequired的状态 所以直接调用
    addFiled(row, row.coord);
  } else {
    ElMessage.error('请先将该字段加入合同范本');
    row.isRequired = !row.isRequired;
  }
}

// 新增字段规则
async function addFiled(row: any, mark: string) {
  if (!formData.value.id) return;

  const params = {
    coord: mark,
    fieldRuleId: row.id,
    isRequired: row.isRequired,
    contractTemplateId: formData.value.id,
  };
  await addContractStandardFieldRule(params);
  getFiledList();
}
// 删除字段规则
async function delFiledClick(row: any) {
  if (!formData.value.id) return;

  const params = {
    contractTemplateId: formData.value.id,
    coord: row.coord,
    fieldRuleId: row.id,
  };
  await delContractStandardFieldRule(params);
  getFiledList();
}

// 查询字段列表
async function searchFiledRule() {
  getFiledList();
}

// 获取字段列表
async function getFiledList() {
  if (!formData.value.id) {
    ElMessage.error('数据错误');
    return;
  }
  tableOptions.loading = true;
  const res = await getStandardFieldRuleList({
    name: searchValue.value,
    contractTemplateId: formData.value.id,
  });
  tableOptions.loading = false;
  tableOptions.data = res;

  const activeRow = tableOptions.data.find(
    (v: any) => v.id === currentItem.value?.id,
  );
  const $grid = tableRef.value;

  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentItem.value = activeRow;
    }
    $grid.setAllTreeExpand(true);
  });
}

// 确认提交
async function insureSubmit() {
  if (formData.value.id) {
    const failData = tableOptions.data.filter(
      (v: any) => !v.isExit && v.isRequired,
    );
    if (failData.length > 0) {
      const data = failData.map((v: any) => v.name);
      ElMessage.error(`请添加必填字段: ${data.join(' | ')}`);
      return;
    }
  }

  const params = {
    name: contractTemplateName.value, // 范本名称
    remark: contractTemplateDesc.value, // 范本说明
    fileId: formData.value.fileId,
  };
  emit('submit', params);
}

// 弹窗打开回调
function handleOpen() {
  initEditor();
}
// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}

// wps配置
declare const WebOfficeSDK: any;
const accessStore = useAccessStore();
// 是否不需要主动刷新token的? 系统token无感刷新后为最新
const refreshToken = () => {
  const token = accessStore.accessToken;
  return new Promise((resolve, reject) => {
    // 异步操作
    setTimeout(() => {
      const success = true;
      if (success) {
        resolve({
          token,
          timeout: 10 * 60 * 1000,
        });
      } else {
        reject(new Error('刷新失败'));
      }
    }, 0);
  });
};

// 获取所有的控件 文本标识
async function getAllControlIds() {
  await instance.ready();
  const app = instance.Application;
  const contentControls = await app.ActiveDocument.ContentControls;
  const count = await contentControls.Count;
  const ids: string[] = [];
  for (let i = 1; i <= count; i++) {
    const control = await contentControls.Item(i);
    // const ID = await control.ID;
    const Tag = await control.Tag;
    // 假设有唯一 id 或用 PlaceholderText 作为标识
    ids.push(Tag);
  }
  return ids;
}

const controlPlaceHoder = ref();
watch(controlPlaceHoder, async (nval, oval) => {
  const deleted = _.uniq(oval).filter((val) => !nval.includes(val));
  if (deleted.length > 0) {
    const deletedItem = deleted[0];
    const filedItem = tableOptions.data.find(
      (v: any) => v.name === deletedItem,
    );
    delFiledClick(filedItem);
  }
});

async function closeClick() {
  // if (formData.value.id) {
  //   const failData = tableOptions.data.filter((v) => !v.isExit && v.isRequired);
  //   if (failData.length > 0) {
  //     const data = failData.map((v) => v.name);
  //     ElMessage.error(`请添加必填字段: ${data.join(' | ')}`);
  //     return;
  //   }
  // }
  drawerVisible.value = false;
}
async function verifyControls() {
  await instance.ready();
  const handleContentChange = _.debounce(async () => {
    controlPlaceHoder.value = await getAllControlIds();
  }, 200); // 200ms内只执行最后一次
  instance.ApiEvent.AddApiEventListener('ContentChange', handleContentChange);
}

const { wpsAppId } = useAppConfig(import.meta.env, import.meta.env.PROD);
const initEditor = async () => {
  isFullScreen.value = false;
  contentLoading.value = true;
  await delay(300);
  const token = accessStore.accessToken;
  const mount = wpsRef.value;
  instance = WebOfficeSDK.init({
    officeType: WebOfficeSDK.OfficeType.Writer,
    appId: wpsAppId,
    fileId: formData.value.fileId,
    mount,
    token,
    refreshToken,
    // mode: 'simple',
    commandBars: [
      {
        cmbId: 'TabInsertTab', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'TabReviewWord', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'TabPageTab', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'ContextMenuConvene', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'BookMarkContextMenu', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
      {
        cmbId: 'InsertTab', // 组件 ID
        attributes: {
          visible: false, // 隐藏组件
          enable: false, // 禁用组件，组件显示但不响应点击事件
        },
      },
    ],
  });
  await instance.ready();

  contentLoading.value = false;
  const app = instance.Application;

  // 将当前文档的编辑状态切换成编辑模式
  app.ActiveDocument.TrackRevisions = false;
  // 获取修订对象
  // const revisions = await app.ActiveDocument.Revisions;

  // 设置修订框显示
  // revisions.ShowRevisionsFrame = false;

  // 验证控件
  verifyControls();
  if (editableValue.value === false) {
    await app.ActiveDocument.SetReadOnly({
      Value: true,
    });
  }
};
async function init({ form, fieldRuleData }: any) {
  // 表单数据赋值
  formData.value = form;
  contractTemplateName.value = formData.value.name;
  contractTemplateDesc.value = formData.value.remark;
  // 字段规则数据赋值
  tableOptions.data = fieldRuleData;

  const $grid = tableRef.value;
  nextTick(() => {
    if ($grid) {
      $grid.setAllTreeExpand(true);
    }
  });
  if (drawerVisible.value === true) {
    initEditor();
  }
}

function delay(time: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, time));
}

defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.el-drawer__body {
  padding: 0;
}
</style>
