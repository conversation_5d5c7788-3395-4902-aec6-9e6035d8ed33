<template>
  <ColPage v-bind="colPageProps">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary" size="small">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 右侧表格区域 -->
      <div
        :style="{ minWidth: '200px', overflow: 'hidden' }"
        v-else
        class="bg-card flex h-full flex-col rounded-lg border p-2"
      >
        <div class="mb-2 flex items-center justify-between">
          <!--  已发布版本不允许编辑 -->
          <ElButton
            :disabled="!actionPermissions.apCreate || projectPublishStatus"
            type="primary"
            size="small"
            @click="handleAddClass"
          >
            新增
          </ElButton>
          <div>
            <ElButton
              v-if="!projectPublishStatus"
              v-auth="actionPermissions.apUpdate"
              type="success"
              size="small"
              @click="publish"
            >
              发布
            </ElButton>
            <ElButton
              v-else
              v-auth="actionPermissions.apUpdate"
              type="primary"
              size="small"
              @click="unPublish"
            >
              取消发布
            </ElButton>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <VxeGrid
            v-bind="leftGridOptions"
            v-on="leftGridEvents"
            ref="leftGridRef"
            @cell-click="handleLeftCellClick"
            @edit-closed="handleLeftEditClosed"
            @menu-click="handleLeftContextMenuClick"
          />
        </div>
      </div>
    </template>
    <div class="bg-card ml-2 flex h-full flex-col rounded-lg border p-2">
      <div class="mb-2 flex items-center justify-between">
        <!--  已发布版本不允许编辑 -->
        <ElButton
          :disabled="!actionPermissions.apCreate || projectPublishStatus"
          type="primary"
          size="small"
          @click="handleAddField"
        >
          新增
        </ElButton>
      </div>
      <div class="flex-1 overflow-hidden">
        <VxeGrid
          v-bind="rightGridOptions"
          v-on="rightGridEvents"
          ref="rightGridRef"
          @cell-click="handleRightCellClick"
          @edit-closed="handleRightEditClosed"
          @menu-click="handleRightContextMenuClick"
        >
          <!--  类型   -->
          <template #typeSlot="{ row }">
            <VxeSelect
              v-model="row.type"
              :options="FieldType"
              :option-props="{ label: 'label', value: 'value' }"
              placeholder="请选择字段类型"
            />
          </template>
          <!--  类型默认值   -->
          <template #typeSlot_default="{ row }">
            <span>{{
              FieldType.find((item) => item.value === row.type)?.label
            }}</span>
          </template>
          <!-- 是否启用 -->
          <template #isEnabled="{ row }">
            <VxeCheckbox
              :disabled="
                !actionPermissions.apUpdate ||
                row.isDefault ||
                projectPublishStatus
              "
              v-model="row.status"
              :checked-value="EnableStatus.ENABLED"
              :unchecked-value="EnableStatus.NOT_ENABLED"
              @change="handleIsEnabledChange(row, $event)"
            />
          </template>
          <!-- 是否必填 -->
          <template #isRequired="{ row }">
            <VxeCheckbox
              :disabled="
                !actionPermissions.apUpdate ||
                row.isDefault ||
                projectPublishStatus
              "
              v-model="row.isRequired"
              :checked-value="true"
              :unchecked-value="false"
              @change="handleIsRequiredChange(row, $event)"
            />
          </template>
          <!-- 枚举值 -->
          <template #enumValueSlot="{ row }">
            <ElInputTag
              :disabled="
                row.type === 'TEXT' ||
                row.type === 'NUMBER' ||
                row.type === 'DATETIME'
              "
              size="small"
              v-model="row.enumValue"
              placeholder="请输入枚举值"
              type="textarea"
              delimiter=","
            />
          </template>
          <template #enumValueSlot_default="{ row }">
            <span>{{ formatEnumValue(row) }}</span>
          </template>
        </VxeGrid>
      </div>
    </div>
  </ColPage>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElInputTag,
  ElMessage,
  ElMessageBox,
  ElTooltip,
} from 'element-plus';

import {
  AddProjectBasicField,
  AddProjectBasicFieldDetail,
  DeleteProjectBasicField,
  DeleteProjectBasicFieldDetail,
  EnableProjectBasicFieldDetail,
  ListProjectBasicField,
  ListProjectBasicFieldDetail,
  MoveProjectBasicField,
  MoveProjectBasicFieldDetail,
  PublishProjectBasicField,
  StatusProjectBasicField,
  UpdateProjectBasicField,
  UpdateProjectBasicFieldDetail,
} from '#/api/enterpriseCenter/enterpriseStandards/field';
import { getCurrentPremission } from '#/utils/permission';

import { colPageProps, EnableStatus, FieldType } from './data';

const { actionPermissions } = getCurrentPremission();

const leftGridRef = ref();
const leftGridOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod: beforeEditMethodFn,
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
  },
  columns: [
    {
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      // 第一遍禁用
      if (projectPublishStatus.value || row.isDefault) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      // 第二遍禁用
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled = !actionPermissions.apDelete && !row._isTempData;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled =
              !actionPermissions.apUpdate ||
              rowIndex === leftGridOptions.data.length - 1;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = !actionPermissions.apUpdate || rowIndex === 0;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  data: [],
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async () => {
        const res = await ListProjectBasicField();
        leftGridOptions.data = res;

        return res;
      },
    },
  },
});
const leftGridEvents = {
  cellMenu({ row }: any) {
    const $grid = leftGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      leftCurrentRow.value = row;
    }
  },
};
const leftCurrentRow = ref({});
function handleLeftCellClick({ row }: any) {
  leftCurrentRow.value = row;
  getFieldList(row.id);
}
async function handleLeftEditClosed({ row }: any) {
  if (leftGridRef.value) {
    const isUpdateRow = leftGridRef.value.isUpdateByRow(row);
    const errMsg = await leftGridRef.value.validate(row);
    if (!errMsg) {
      let res = null;
      if (row.id) {
        // 数据是否发生变化
        if (isUpdateRow) {
          res = await UpdateProjectBasicField(row.id, { name: row.name });
        }
      } else {
        res = await AddProjectBasicField({ name: row.name });
      }
      if (res) ElMessage.success('操作成功！');
      leftGridRef.value.commitProxy('query');
    }
  }
}
async function handleLeftContextMenuClick({ menu }: any) {
  const currentRow: any = leftCurrentRow.value;
  switch (menu.code) {
    case 'DELETE_ROW': {
      if (currentRow.id) {
        ElMessageBox.confirm('确定删除该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await DeleteProjectBasicField(currentRow.id);
          if (res) ElMessage.success('操作成功！');
          leftGridRef.value.commitProxy('query');
        });
      } else {
        // id 不存在临时数据删除
        return (leftGridOptions.data = leftGridOptions.data.filter(
          (item: any) => item.id,
        ));
      }
      break;
    }
    case 'MOVE_DOWN': {
      await leftGridRef.value.moveRowTo(currentRow, 1);
      await MoveProjectBasicField(currentRow.id, 'down');
      leftGridRef.value.commitProxy('query');
      break;
    }
    case 'MOVE_UP': {
      await leftGridRef.value.moveRowTo(currentRow, -1);
      await MoveProjectBasicField(currentRow.id, 'up');
      leftGridRef.value.commitProxy('query');
      break;
    }
  }
}

// 右侧新增
async function handleAddClass() {
  const errMsg = await leftGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  // 构建新数据
  const newClassify = {
    _isTempData: true,
    name: '',
  };
  await nextTick(() => {
    leftGridRef.value && leftGridRef.value.insertAt(newClassify, -1);
    leftGridRef.value.validate(leftCurrentRow.value);
  });
}

const rightCurrentRow = ref({});
const rightGridRef = ref();
const rightGridOptions = reactive<any>({
  height: '100%',
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
    beforeEditMethod: beforeEditMethodFn,
  },
  editRules: {
    name: [{ required: true, message: '字段名称不得为空！' }],
    type: [{ required: true, message: '字段类型不得为空！' }],
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      // 第一遍禁用
      if (projectPublishStatus.value || row.isDefault) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      // 第二遍禁用
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled = !actionPermissions.apDelete && !row._isTempData;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled =
              !actionPermissions.apUpdate ||
              rowIndex === rightGridOptions.data.length - 1;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = !actionPermissions.apUpdate || rowIndex === 0;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  columns: [
    { type: 'seq', width: 70, title: '序号' },
    {
      title: '字段名称',
      field: 'name',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入字段名称',
        },
      },
    },
    {
      field: 'type',
      title: '字段类型',
      editRender: {},
      slots: {
        edit: 'typeSlot',
        default: 'typeSlot_default',
      },
      filters: [
        { label: '文本', value: 'TEXT' },
        { label: '数字', value: 'NUMBER' },
        { label: '日期', value: 'DATETIME' },
        { label: '下拉-多选', value: 'ENUM_MULTIPLE_CHOICE' },
        { label: '下拉-单选', value: 'ENUM_SINGLE_CHOICE' },
      ],
      filterMethod({ option, row, column }: any) {
        return `${row[column.field]}`.includes(option.value);
      },
    },
    {
      field: 'unit',
      title: '单位',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入单位',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'isRequired',
      title: '是否必填',
      slots: {
        default: 'isRequired',
      },
    },
    {
      field: 'enumValue',
      title: '枚举值',
      minWidth: 150,
      editRender: {},
      slots: {
        edit: 'enumValueSlot',
        default: 'enumValueSlot_default',
      },
    },
    {
      field: 'placeholder',
      title: '字段提示',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入字段提示',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'description',
      title: '字段含义',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入字段含义',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'isEnabled',
      title: '是否启用',
      slots: {
        default: 'isEnabled',
      },
    },
  ],
  data: [],
});
const rightGridEvents = {
  cellMenu({ row }: any) {
    const $grid = rightGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      rightCurrentRow.value = row;
    }
  },
};
function formatEnumValue(row: any) {
  if (Array.isArray(row.enumValue)) {
    return row.enumValue.join(',');
  }
  return '';
}
function handleRightCellClick({ row }: any) {
  rightCurrentRow.value = row;
}
async function handleRightEditClosed({ row }: any) {
  if (rightGridRef.value) {
    const isUpdateRow = rightGridRef.value.isUpdateByRow(row);
    const errMsg = await rightGridRef.value.validate(row);
    if (!errMsg) {
      let res = null;
      if (row.id) {
        // 数据是否发生变化
        if (isUpdateRow) {
          res = await UpdateProjectBasicFieldDetail(row.id, {
            name: row.name,
            type: row.type,
            isRequired: row.isRequired,
            unit: row.unit,
            enumValue: Array.isArray(row.enumValue)
              ? row.enumValue.join(',')
              : row.enumValue,
            placeholder: row.placeholder,
            description: row.description,
            status: row.status,
            basicProjectInfoCategoryId: row.basicProjectInfoCategoryId,
          });
        }
      } else {
        res = await AddProjectBasicFieldDetail({
          name: row.name,
          type: row.type,
          isRequired: row.isRequired,
          unit: row.unit,
          enumValue: Array.isArray(row.enumValue)
            ? row.enumValue.join(',')
            : row.enumValue,
          placeholder: row.placeholder,
          description: row.description,
          status: row.status,
          basicProjectInfoCategoryId: row.basicProjectInfoCategoryId,
        });
      }
      if (res) ElMessage.success('操作成功！');
      getFieldList(leftCurrentRow.value.id);
    }
  }
}
async function handleRightContextMenuClick({ menu }: any) {
  const currentRow = rightCurrentRow.value;
  switch (menu.code) {
    case 'DELETE_ROW': {
      if (currentRow.id) {
        ElMessageBox.confirm('确定删除该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await DeleteProjectBasicFieldDetail(
            rightCurrentRow.value.id,
          );
          if (res) ElMessage.success('操作成功！');
          await getFieldList(leftCurrentRow.value.id);
        });
      } else {
        // id 不存在临时数据删除
        return (rightGridOptions.data = rightGridOptions.data.filter(
          (item: any) => item.id,
        ));
      }
      break;
    }
    case 'MOVE_DOWN': {
      await rightGridRef.value.moveRowTo(rightCurrentRow.value, 1);
      await MoveProjectBasicFieldDetail(rightCurrentRow.value.id, 'down');
      await getFieldList(leftCurrentRow.value.id);
      break;
    }
    case 'MOVE_UP': {
      await rightGridRef.value.moveRowTo(rightCurrentRow.value, -1);
      await MoveProjectBasicFieldDetail(rightCurrentRow.value.id, 'up');
      await getFieldList(leftCurrentRow.value.id);
      break;
    }
  }
}
// 右侧新增
async function handleAddField() {
  const errMsg = await rightGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  // 构建新数据
  const newFieldRow = {
    _isTempData: true,
    basicProjectInfoCategoryId: leftCurrentRow.value.id,
    name: '',
    type: '',
    isRequired: false,
    unit: '',
    enumValue: '',
    placeholder: '',
    description: '',
    status: EnableStatus.ENABLED,
  };
  await nextTick(() => {
    rightGridRef.value && rightGridRef.value.insertAt(newFieldRow, -1);
    rightGridRef.value.validate(rightCurrentRow.value);
  });
}
// 获取字段明细数据
async function getFieldList(leftCurrentRowId: string) {
  const filedDetails = await ListProjectBasicFieldDetail(leftCurrentRowId);
  rightGridOptions.data = filedDetails.map((item: any) => {
    return {
      ...item,
      enumValue: item.enumValue ? item.enumValue.split(',') : [],
    };
  });
}
// 是否启用
async function handleIsEnabledChange(row: any, { value }: any) {
  const res = await EnableProjectBasicFieldDetail(row.id, value);
  if (res) ElMessage.success('操作成功！');
}
// 是否必填
async function handleIsRequiredChange(row: any, { value }: any) {
  const res = await UpdateProjectBasicFieldDetail(row.id, {
    isRequired: value,
    name: row.name,
    type: row.type,
  });
  if (res) ElMessage.success('操作成功！');
}

// 发布 - 全量发布所有分组
async function publish() {
  const res = await PublishProjectBasicField(true);
  if (res) ElMessage.success('发布成功！');
  leftGridRef.value.commitProxy('query');
  await getStatus();
}
// 取消发布
async function unPublish() {
  const res = await PublishProjectBasicField(false);
  if (res) ElMessage.success('取消发布成功！');
  leftGridRef.value.commitProxy('query');
  await getStatus();
}

// 分组状态 true 全部已发布 false 未发布
const projectPublishStatus = ref(false);
async function getStatus() {
  projectPublishStatus.value = await StatusProjectBasicField();
}
getStatus();

// 根据发布状态的是否允许编辑
function beforeEditMethodFn({ row }: any) {
  // 编辑权限（忽略临时数据）
  if (!actionPermissions.apUpdate && !row._isTempData) {
    return false;
  }
  // 如果是已发布的版本，则不允许编辑
  if (projectPublishStatus.value) {
    return false;
  }
  // 内置节点判断
  if (row.isDefault) {
    return false;
  }
  return true;
}
function visibleMethodFn({ row, options, rowIndex }: any) {
  //   已发布的状态没有按钮
  if (projectPublishStatus.value) {
    return options;
  }
  // 内置节点判断
  if (row.isDefault) {
    return options;
  }

  return true;
}
</script>

<style lang="scss" scoped></style>
