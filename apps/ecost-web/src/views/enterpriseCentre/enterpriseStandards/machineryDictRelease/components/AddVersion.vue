<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    title="维护版本"
    :destroy-on-close="false"
    :width="600"
    @close="handleClose"
  >
    <div class="mb-2 flex items-center justify-between">
      <div>
        <ElButton
          v-auth="actionPermissions.apCreate"
          type="primary"
          size="small"
          @click="addNewVersion"
        >
          新增
        </ElButton>
        <ElButton
          :disabled="!actionPermissions.apUpdate || isEnable"
          type="primary"
          size="small"
          @click="handleEnabledOrNotEnabled"
        >
          {{ showRowStatusText }}
        </ElButton>
      </div>
      <ElButton
        :disabled="!actionPermissions.apDelete || isDisabledBtn"
        type="danger"
        :icon="Delete"
        size="small"
        @click="deltetRow"
      />
    </div>
    <VxeGrid
      v-bind="gridVersionOptions"
      ref="gridVersionRef"
      @cell-click="handleClickRow"
      @edit-closed="handleRowEditClosed"
    >
      <template #businessCost="{ row }">
        <vxe-select
          v-model="row.businessCostSubjectVersionId"
          :options="businessVersionList"
          :option-props="{
            label: 'name',
            value: 'id',
          }"
        />
        <!-- :disabled="" -->
        <!-- <el-select v-model="row.businessCostSubjectVersionId" :options="businessVersionList" :option-props="{
          label: 'name',
          value: 'id',
          disabled: (row: any) => row.status === EnableStatus.ENABLED
        }">
          <el-option v-for="(item) in businessVersionList" :key="item.id" :label="item.name" :value="item.id"
            :disabled="item.status === EnableStatus.ENABLED"></el-option>
        </el-select> -->
      </template>
      <template #businessCost_default="{ row }">
        <span>{{
          formateBusinessCostName(row.businessCostSubjectVersionId)
        }}</span>
      </template>
      <template #status="{ row }">
        <ElTag
          size="small"
          v-if="row.status === EnableStatus.ENABLED"
          type="success"
        >
          已启用
        </ElTag>
        <ElTag
          v-else-if="row.status === EnableStatus.DISABLED"
          size="small"
          type="warning"
        >
          已停用
        </ElTag>
        <ElTag
          v-else-if="row.status === EnableStatus.NOT_ENABLED"
          size="small"
          type="danger"
        >
          未启用
        </ElTag>
      </template>
    </VxeGrid>
    <template #footer>
      <ElButton size="small" @click="handleClose">关闭</ElButton>
    </template>
  </ElDialog>
</template>

<script lang="ts" setup>
import { computed, nextTick, reactive, ref, watch } from 'vue';

import { Delete } from '@element-plus/icons-vue';
import {
  ElButton,
  ElDialog,
  ElMessage,
  ElMessageBox,
  ElTag,
} from 'element-plus';
import _ from 'lodash';

import { ListBusinessCostVersion } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  AddMachineryDicVersion,
  DelMachineryDicVersion,
  ListMachineryDicVersion,
  UpdateMachineryDicVersion,
} from '#/api/enterpriseCenter/enterpriseStandards/machineryDictRelease';
import { getCurrentPremission } from '#/utils/permission';

import { EnableStatus } from '../data';

const props = withDefaults(
  defineProps<{
    visible: boolean;
  }>(),
  {
    visible: false,
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const { actionPermissions } = getCurrentPremission();

const dialogVisible = ref(props.visible);
const gridVersionRef = ref();
const gridVersionOptions = reactive<any>({
  height: 500,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod: ({ row }: any) => {
      // 编辑权限（忽略临时数据）
      if (!actionPermissions.apUpdate && !row._isTempData) {
        return false;
      }
      // 已经启用的数据无法编辑
      if (row.status === EnableStatus.ENABLED) {
        return false;
      }
      return true;
    },
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
  },
  columns: [
    {
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    {
      width: '75px',
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
    {
      width: '165px',
      field: 'businessCostAccountId',
      title: '业务成本科目版本',
      editRender: {},
      slots: {
        edit: 'businessCost',
        default: 'businessCost_default',
      },
    },
    {
      width: '100px',
      field: 'projectRefCount',
      title: '项目引用数量',
    },
  ],
  data: [],
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async () => {
        return await ListMachineryDicVersion();
      },
    },
  },
});
// 已选中版本信息
const currentVersion = ref({
  id: '',
  status: '',
  quoteNumber: 0,
});
function handleClickRow({ row }: any) {
  currentVersion.value = row;
}
// 行数据编辑关闭
async function handleRowEditClosed({ row }: any) {
  if (gridVersionRef.value) {
    const errMsg = await gridVersionRef.value.validate(row);
    if (!errMsg) {
      let res = null;
      try {
        res = await (row.id
          ? UpdateMachineryDicVersion(row.id, {
              name: row.name,
              // taxrateDictionaryVersionId: row.taxrateDictionaryVersionId,
              // businessCostSubjectVersionName: row.businessCostSubjectVersionName,
              businessCostSubjectVersionId: row.businessCostSubjectVersionId,
            })
          : AddMachineryDicVersion({
              name: row.name,
              // taxrateDictionaryVersionId: row.taxrateDictionaryVersionId,
              // businessCostSubjectVersionName: row.businessCostSubjectVersionName,
              businessCostSubjectVersionId: row.businessCostSubjectVersionId,
            }));
        if (res) ElMessage.success('操作成功！');
        gridVersionRef.value.commitProxy('query');
      } catch {
        gridVersionRef.value.revertData(row);
      }
      emit('refresh');
    }
  }
}

// 新增版本信息
async function addNewVersion() {
  const errMsg = await gridVersionRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newRow = {
    _isTempData: true,
    name: '',
    taxrateDictionaryVersionId: '',
    businessCostSubjectVersionName: '',
    businessCostSubjectVersionId: '',
    status: EnableStatus.NOT_ENABLED,
  };
  await nextTick(() => {
    gridVersionRef.value && gridVersionRef.value.insertAt(newRow);
    gridVersionRef.value.validate(true);
  });
}

// 按钮禁用
const isDisabledBtn = computed(() => {
  if (_.isEmpty(currentVersion.value)) {
    return true;
  } else {
    // 已启用的数据无法删除
    return currentVersion.value.id
      ? currentVersion.value.status === EnableStatus.ENABLED
      : false;
  }
});
// 删除
async function deltetRow() {
  // 删除临时数据
  if (!currentVersion.value.id || currentVersion.value.id === '') {
    return (gridVersionOptions.data = gridVersionOptions.data.filter(
      (item: any) => item.id,
    ));
  }
  // 删除已有数据
  if (currentVersion.value.quoteNumber > 0) {
    return ElMessage.warning('版本被引用不可删除!');
  }
  ElMessageBox.confirm('确定删除该数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await DelMachineryDicVersion(currentVersion.value.id);
    if (res) {
      ElMessage.success('操作成功！');
    }
    emit('refresh');
    gridVersionRef.value.commitProxy('query');
  });
}

const showRowStatusText = computed(() => {
  // 启停用文本
  if (currentVersion.value.id) {
    switch (currentVersion.value.status) {
      case EnableStatus.DISABLED: {
        return '启用';
      }
      case EnableStatus.ENABLED: {
        return '停用';
      }
      case EnableStatus.NOT_ENABLED: {
        return '启用';
      }
      default: {
        return '启用';
      }
    }
  } else {
    return '启用';
  }
});
const isEnable = computed(() => {
  return _.isEmpty(currentVersion.value) ? true : !currentVersion.value.id;
});

// 启用or停用
async function handleEnabledOrNotEnabled() {
  const { status, id } = currentVersion.value;
  const res = await UpdateMachineryDicVersion(id, {
    status:
      status === EnableStatus.DISABLED || status === EnableStatus.NOT_ENABLED
        ? EnableStatus.ENABLED
        : EnableStatus.DISABLED,
  });
  if (res) ElMessage.success('操作成功！');

  emit('refresh');
  gridVersionRef.value.commitProxy('query');
}

// 获取业务成本科目版本
const businessVersionList = ref([]);
async function getBusinessCostSubject() {
  const res = await ListBusinessCostVersion();
  // res.forEach((item: any) => {
  //   if (item.status == EnableStatus.NOT_ENABLED) {
  //     item.name = item.name + '(已停用)'
  //   }
  //   item.disabled = item.status == EnableStatus.NOT_ENABLED ? true : false
  // })
  businessVersionList.value = res;
}
function formateBusinessCostName(versionId: string) {
  const findItem: any = businessVersionList.value.find(
    (item: any) => item.id === versionId,
  );
  return findItem && findItem.name;
}
getBusinessCostSubject();

function handleClose() {
  emit('update:visible', false);
}

watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
</script>

<style lang="scss" scoped>
:deep(.vxe-table) {
  :deep(.vxe-select--panel.is--transfer),
  :deep(.vxe-date-picker--panel.is--transfer) {
    z-index: 99999 !important;
  }
}

:deep(.vxe-select--panel.is--transfer),
:deep(.vxe-date-picker--panel.is--transfer) {
  z-index: 99999 !important;
}
</style>
