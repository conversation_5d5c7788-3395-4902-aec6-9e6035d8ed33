<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    title="财务成本科目"
    :destroy-on-close="false"
    :width="600"
    @close="handleClose"
  >
    <VxeGrid v-bind="gridOptions" ref="gridRef">
      <template #typeSlot="{ row }">
        <ElTag :type="row.parentId ? 'primary' : 'success'">
          {{ row.parentId ? '明细' : '分类' }}
        </ElTag>
      </template>
    </VxeGrid>
    <template #footer>
      <ElButton size="small" @click="handleClose">关闭</ElButton>
    </template>
  </ElDialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { ElButton, ElDialog, ElTag } from 'element-plus';

import { ListFinancialCostAccount } from '#/api/enterpriseCenter/enterpriseStandards/financialCostAccount';

const props = withDefaults(
  defineProps<{
    visible: boolean;
  }>(),
  {
    visible: false,
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();
const gridRef = ref();
const dialogVisible = ref(props.visible);

const gridOptions = reactive({
  size: 'mini',
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  align: 'center',
  maxHeight: 500,
  border: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columns: [
    {
      treeNode: true,
      type: 'seq',
      fixed: 'left',
      width: 100,
    },
    {
      field: 'type',
      title: '类别',
      slots: {
        default: 'typeSlot',
      },
    },
    {
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '财务成本科目名称',
    },
  ],
  toolbarConfig: {
    refresh: true,
  },
  data: [],
  proxyConfig: {
    response: {},
    ajax: {
      query: () => {
        return ListFinancialCostAccount();
      },
    },
  },
});

watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
function handleClose() {
  emit('update:visible', false);
}
</script>

<style lang="scss" scoped></style>
