<template>
  <AuthenticationForgetPassword
    ref="forgetPwdRef"
    :form-schema="formSchema"
    :loading="loading"
    :countdown="countdown"
    @submit="handleSubmit"
  />
</template>

<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, ref, useTemplateRef } from 'vue';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { ElMessage } from 'element-plus';

import { resetPassword, sendCode } from '#/api/authorization/author';
import { router } from '#/router';

defineOptions({ name: 'ForgetPassword' });

const forgetPwdRef =
  useTemplateRef<InstanceType<typeof AuthenticationForgetPassword>>(
    'forgetPwdRef',
  );

const loading = ref(false);
const CODE_LENGTH = 6;
const countdown = ref(0);

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.mobile'),
      },
      fieldName: 'phone',
      label: $t('authentication.mobile'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.mobileTip') })
        .regex(/^1[3-9]\d{9}$/, {
          message: $t('authentication.mobileErrortip'),
        }),
    },
    {
      component: 'VbenPinInput',
      componentProps: {
        codeLength: CODE_LENGTH,
        createText: (countdown: number) => {
          const text =
            countdown > 0
              ? $t('authentication.sendText', [countdown])
              : $t('authentication.sendCode');
          return text;
        },
        handleSendCode: async () => {
          loading.value = true;

          const formApi = forgetPwdRef.value?.getFormApi();
          if (!formApi) {
            loading.value = false;
            throw new Error('formApi is not ready');
          }
          await formApi.validateField('phone');
          const isPhoneReady = await formApi.isFieldValid('phone');
          if (!isPhoneReady) {
            loading.value = false;
            throw new Error('Phone number is not Ready');
          }
          const { phone } = await formApi.getValues();
          await sendCode({ phone });

          loading.value = false;
        },
        placeholder: $t('authentication.code'),
      },
      fieldName: 'code',
      label: $t('authentication.code'),
      rules: z.string().length(CODE_LENGTH, {
        message: $t('authentication.codeTip', [CODE_LENGTH]),
      }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.newPasswordTip'),
        type: 'password',
      },
      fieldName: 'newPassword',
      label: $t('authentication.newPassword'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.newPasswordTip') }),
    },
  ];
});

// 提交
async function handleSubmit(value: Recordable<any>) {
  const res = await resetPassword(value);
  if (res) {
    ElMessage.success($t('authentication.forgetPasswordSuccessTip'));
    router.push('/auth/login');
  }
}
</script>
