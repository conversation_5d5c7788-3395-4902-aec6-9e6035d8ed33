<template>
  <div>
    <AuthenticationLogin
      :form-schema="formSchema"
      :show-qrcode-login="false"
      :show-code-login="false"
      :show-register="false"
      :show-third-party-login="false"
      :loading="loginLoading"
      @submit="preLogin"
    />

    <ElDialog
      style="width: 500px"
      v-model="isOrgSelectShow"
      :append-to-body="true"
    >
      <div class="login-org-container pt-6">
        <div class="login-org-content mb-6 flex w-full items-center">
          <div class="w-[200px] text-[16px]">选择登录的企业</div>
          <div class="w-full">
            <ElInput
              v-model="filterText"
              style="width: 100%"
              placeholder="输入查询名称"
            />
          </div>
        </div>
        <div class="mb-6">
          <ElTree
            style="width: 100%"
            ref="treeRef"
            class="custom-tree"
            :data="data"
            :props="defaultProps"
            default-expand-all
            :expand-on-click-node="false"
            node-key="id"
            :filter-node-method="filterNode"
          >
            <template #default="{ data }">
              <div class="item-name">
                {{ data.name }}
              </div>
            </template>
          </ElTree>
        </div>
        <div>
          <ElButton type="primary" @click="login">确认</ElButton>
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<script lang="ts" setup>
import type { TreeInstance } from 'element-plus';

import type { VbenFormSchema } from '@vben/common-ui';
import type { BasicOption, Recordable } from '@vben/types';

import type { TreeNode } from '#/views/systemCenter/systemManagement/org-user/types';

import { computed, ref, watch } from 'vue';

import { AuthenticationLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { ElButton, ElDialog, ElInput, ElTree } from 'element-plus';

import { preLoginApi } from '#/api/';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();

const MOCK_USER_OPTIONS: BasicOption[] = [
  {
    label: 'Super',
    value: 'vben',
  },
  {
    label: 'Admin',
    value: 'admin',
  },
  {
    label: 'User',
    value: 'jack',
  },
];

const formSchema = computed((): VbenFormSchema[] => {
  return [
    // {
    //   component: 'VbenSelect',
    //   componentProps: {
    //     options: MOCK_USER_OPTIONS,
    //     placeholder: $t('authentication.selectAccount'),
    //   },
    //   fieldName: 'selectAccount',
    //   label: $t('authentication.selectAccount'),
    //   rules: z
    //     .string()
    //     .min(1, { message: $t('authentication.selectAccount') })
    //     .optional()
    //     .default('vben'),
    // },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      dependencies: {
        trigger(values, form) {
          if (values.selectAccount) {
            const findUser = MOCK_USER_OPTIONS.find(
              (item) => item.value === values.selectAccount,
            );
            if (findUser) {
              form.setValues({
                password: '123456',
                username: findUser.value,
              });
            }
          }
        },
        triggerFields: ['selectAccount'],
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    // {
    //   component: markRaw(SliderCaptcha),
    //   fieldName: 'captcha',
    //   rules: z.boolean().refine((value) => value, {
    //     message: $t('authentication.verifyRequiredTip'),
    //   }),
    // },
  ];
});

const filterText = ref('');
const treeRef = ref<TreeInstance>();
const defaultProps = {
  children: 'children',
  label: 'name',
};
const loginLoading = ref(false);
watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: TreeNode) => {
  if (!value) return true;
  return data.name.includes(value);
};
const data = ref<TreeNode[]>([]);

const isOrgSelectShow = ref(false);

const userInfo = {
  username: '',
  password: '',
};

const preLogin = async function (
  params: Recordable<any>,
  onSuccess?: () => Promise<void> | void,
) {
  loginLoading.value = true;
  userInfo.username = params.username;
  userInfo.password = params.password;

  const res = await preLoginApi(params).catch(() => {
    loginLoading.value = false;
  });
  if (res) {
    const tenants = res.tenants;
    loginLoading.value = false;
    data.value = tenants;
    if (res.tenants.length === 1) {
      params.tenantId = res.tenants[0].id;
      authStore.authLogin(params, onSuccess);
    } else {
      isOrgSelectShow.value = true;
    }
  }
};

const login = async function () {
  const selectedNode = treeRef.value!.getCurrentKey();
  const params = {
    tenantId: selectedNode,
    username: userInfo.username,
    password: userInfo.password,
  };
  authStore.authLogin(params);
  isOrgSelectShow.value = false;
};
</script>
<style lang="scss" scoped>
.login-org-container {
  .custom-tree {
    // 调整箭头位置到右边
    :deep(.el-tree-node__content) {
      flex-direction: row-reverse;
      justify-content: space-between;
      height: 42px;
      padding-right: 8px;
      padding-left: 8px !important;
      font-size: 18px;
    }

    // 调整展开箭头的位置和样式
    :deep(.el-tree-node__expand-icon) {
      padding: 0;
      margin-left: 8px;

      &.expanded {
        transform: rotate(90deg);
      }

      &:not(.expanded) {
        transform: rotate(0deg);
      }
    }

    // 调整 label 的位置
    :deep(.el-tree-node__label) {
      margin-left: 8px;
    }
  }
}

:deep(.custom-tree .el-tree-node.is-current > .el-tree-node__content) {
  color: #007acc; /* 深蓝字体颜色 */
  background-color: #e0f7fa; /* 浅蓝色背景 */
}
</style>
