<template>
  <ColPage v-bind="colPageSettings">
    <!-- 左侧树 -->
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary" size="small">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <div
        :style="{ minWidth: '300px' }"
        v-else
        class="bg-card h-full rounded-lg border p-2"
      >
        <!-- 角色树区域 -->
        <div class="mb-2 mt-2 flex">
          <TreeLevelExpand
            :expand-idx="expandIdx"
            @expand-click="expandClick"
          />
          <ElInput
            class="ml-5 flex-1"
            placeholder="请输入内容"
            v-model="filterText"
          >
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput>
        </div>
        <div class="tree-content">
          <ElScrollbar height="680px">
            <ContextMenu
              ref="contextMenu"
              @select="contextMenuChoice"
              :menu="TREE_NODE_TYPE"
            >
              <ElTree
                ref="treeRef"
                class="filter-tree min-w-[360px]"
                :default-expanded-keys="defaultExpendKeys"
                :data="treeData"
                :props="treeDefaultSetting"
                :expand-on-click-node="false"
                default-expand-all
                highlight-current
                :filter-node-method="filterNode"
                node-key="id"
                @node-click="treeNodeClick"
                @node-contextmenu="contextMenuClick"
              >
                <template #default="{ data }">
                  <div class="flex w-full items-center justify-between">
                    <div class="item-name w-[140px]">
                      <span>{{ data.name }}</span>
                      <span class="item-nums" v-if="data.children?.length > 0">
                        <span class="color-[orage] in">{{
                          `(${data.children?.length})`
                        }}</span>
                      </span>
                    </div>

                    <div
                      class="item-icon-group flex w-[110px] items-center justify-between pr-[10px]"
                    >
                      <div class="item-icon">
                        <IconifyIcon
                          v-if="data.type === OrgType.TENANT"
                          class="text-l"
                          icon="bi:bank"
                        />
                        <IconifyIcon
                          v-if="data.type === OrgType.COMPANY"
                          class="text-l"
                          icon="bi:building"
                        />
                        <IconifyIcon
                          v-if="data.type === OrgType.PROJECT"
                          class="text-l"
                          icon="bi:house-door"
                        />
                      </div>

                      <div class="item-nums" v-if="data.count">
                        <span>{{ data.count }}个</span>
                      </div>
                      <div clss="item-disable">
                        <IconifyIcon
                          v-if="data.isHide"
                          class="text-l"
                          icon="bi:eye-slash-fill"
                        />
                        <IconifyIcon
                          v-else
                          class="text-l opacity-0"
                          icon="bi:eye-slash-fill"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </ElTree>
            </ContextMenu>
          </ElScrollbar>
        </div>
      </div>
    </template>
    <!-- 右侧表格 -->
    <div class="bg-card ml-2 h-full rounded-lg border p-2 pb-14">
      <VxeGrid
        ref="orgTableRef"
        v-bind="tableOptions"
        v-on="gridEvents"
        :loading="tableLoading"
      >
        <template #top>
          <div class="flex items-center justify-between">
            <div class="mb-2">
              <ElButton
                :disabled="!actionPermissions.apCreate || addBtnDisabled"
                type="primary"
                size="small"
                @click="addUserHandle"
              >
                添加用户
              </ElButton>
              <ElButton
                type="primary"
                size="small"
                :disabled="!actionPermissions.apCreate || addBtnDisabled"
                @click="selectUserHandle"
              >
                选择用户
              </ElButton>
              <ElButton
                size="small"
                :disabled="addBtnDisabled"
                @click="showAllHandle"
              >
                {{ showALlVisible ? '关闭显示全部用户' : '显示全部用户' }}
              </ElButton>
              <ElButton size="small" @click="filterHandle">
                <div>{{ isFilter ? '筛选' : '取消筛选' }}</div>
              </ElButton>
            </div>
            <div class="flex">
              <ElButton
                size="small"
                :disabled="!actionPermissions.apExport"
                @click="exportExcel"
              >
                导出
              </ElButton>
              <ElButton
                size="small"
                :disabled="!actionPermissions.apImport"
                @click="downloadExcelTemplate"
              >
                下载模板
              </ElButton>
              <ElButton
                size="small"
                @click="importExcel"
                :disabled="!actionPermissions.apImport || addBtnDisabled"
              >
                导入
              </ElButton>
            </div>
          </div>
        </template>

        <template #orgSetting="{ row }">
          <OrgSelector
            v-model="row.orgIdArr"
            :multiple="true"
            :disabled="!actionPermissions.apUpdate"
            @change="
              () => {
                changeUserOrg(row);
              }
            "
          />
        </template>
        <template #roleSetting="{ row }">
          <RoleSelector
            v-model="row.roleIdArr"
            :collapse-tags="true"
            :max-collapse-tags="2"
            :disabled="!actionPermissions.apUpdate"
            @change="
              (cur, type) => {
                changeUserRole(row, cur, type);
              }
            "
          />
        </template>
        <template #statusSetting="{ row }">
          <div>
            <ElTag
              :type="
                row.status === UserStatus.ACTIVE
                  ? 'success'
                  : row.status === UserStatus.DEACTIVE
                    ? 'danger'
                    : 'primary'
              "
            >
              {{ getStatusLabel(row.status) }}
            </ElTag>
          </div>
        </template>
        <template #operation="{ row }">
          <ElRadioGroup
            class="status-group"
            v-model="row.status"
            size="small"
            :disabled="!actionPermissions.apUpdate"
            @change="changeUserStatus(row)"
          >
            <ElRadio :value="UserStatus.ACTIVE">启用</ElRadio>
            <ElRadio :value="UserStatus.DEACTIVE">作废</ElRadio>
            <ElRadio :value="UserStatus.FREEZE">暂停</ElRadio>
          </ElRadioGroup>
        </template>
      </VxeGrid>
      <vxe-pager
        size="mini"
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :total="tableTotal"
        @page-change="pageChange"
      />
    </div>
    <!-- 弹窗表单 -->
    <AddOrEditOrg
      v-if="orgFromDialogVisible"
      v-model:visible="orgFromDialogVisible"
      :form-data="editForm"
      @refresh="refreshTree"
    />

    <UserDrawer
      v-if="userDrawerVisible"
      v-model:visible="userDrawerVisible"
      :form-data="userDrawerForm"
      @refresh="refreshTable"
    />

    <input
      ref="fileEl"
      type="file"
      class="hidden"
      accept=".xlsx,.XLSX"
      @change="handleFileChange"
    />
  </ColPage>
</template>

<script lang="ts" setup>
import type { TreeInstance, TreeStoreNodesMap } from 'element-plus';

import type { TreeNode } from './types';

import { nextTick, onBeforeMount, reactive, ref, watch } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useUserStore } from '@vben/stores';
import { downloadFileFromBlobPart } from '@vben/utils';

import {
  ElButton,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElRadio,
  ElRadioGroup,
  ElScrollbar,
  ElTag,
  ElTooltip,
  ElTree,
} from 'element-plus';

import { consumAciveCode } from '#/api/authorization/author';
import {
  boolOrg,
  delOrg,
  getOrganizationTree,
} from '#/api/systemManagementApi/organizationApis';
import userApi from '#/api/systemManagementApi/userManagementApis';
import ContextMenu from '#/components/ContextMenu/index.vue';
import OrgSelector from '#/components/OrgSelector/index.vue';
import RoleSelector from '#/components/RoleSelector/index.vue';
import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import { useCommonStore } from '#/store';
import { downloadLocalFile } from '#/utils';
import { getIdsByLevel } from '#/utils/common';
import { getCurrentPremission } from '#/utils/permission';
import { idCardRegex, phoneRegex } from '#/utils/regex';

import AddOrEditOrg from './components/AddOrEditOrg.vue';
import UserDrawer from './components/UserDrawer.vue';
import { OrgType, UserStatus } from './types';

defineOptions({
  name: 'OrgUser',
});

const { actionPermissions } = getCurrentPremission();

//  ------ Type定义 ------
interface UserItem {
  id?: string;
  nickname: string;
  phone: string;
  idCard: string;
  orgs: Array<{ id: string }>;
  roles: Array<{ id: string }>;
  status: UserStatus;

  roleIdArr: string[];
  orgIdArr: string[];
}
interface editForm {
  id: null | string;
  name: string;
  authCode: string;
  sealName: string;
  type: string;
  parentId: string;
  isHide: string;
  sort: string;
}
interface menuType {
  label: string;
  disabled: boolean;
  type: string;
}
interface userDrawerForm {
  orgIds: string;
}
//  ------ 公共数据 ------
const calculateCount = (nodes: any[]): number => {
  let total = 0;
  nodes.forEach((node) => {
    // 如果有子节点，递归计算子节点的 count
    if (node.children && node.children.length > 0) {
      node.count = calculateCount(node.children);
    }
    // 如果当前节点是 'project' 类型，累加到 total
    if (node.type === 'PROJECT') {
      total += 1;
    }
    // 累加子节点的 count
    if (node.count) {
      total += node.count;
    }
  });
  return total;
};

const userStore = useUserStore();
const commonStore = useCommonStore();

const getStatusLabel = (status: UserStatus) => {
  const statusMap = {
    [UserStatus.ACTIVE]: '启用',
    [UserStatus.DEACTIVE]: '作废',
    [UserStatus.FREEZE]: '暂停',
  };
  return statusMap[status] || '未知状态';
};
// ------ Page页配置 ------
const colPageSettings = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMinWidth: 24,
  leftWidth: 30,
  resizable: true,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});
// ------ 用户表格数据 ------

const tableLoading = ref(false);
const orgTableRef = ref();
const addBtnDisabled = ref(true);
const tableTotal = ref(0);

interface QueryParams {
  page: number;
  pageSize: number;
  orgIds: string;
  nickname: string;
  idCard: string;
  phone: string;
  status: string;

  [key: string]: any;
}
const queryParams: QueryParams = {
  // 查询参数
  page: 1,
  pageSize: 100,
  orgIds: '',
  nickname: '',
  idCard: '',
  phone: '',
  status: '',
};
const isFilter = ref(true);
const showALlVisible = ref(false);
let isFinishInsert = true; // 是否完成了新增
// 表头配置
const columns = [
  {
    type: 'seq',
    width: '60',
  },
  {
    field: 'nickname',
    title: '用户姓名',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入用户姓名',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入姓名',
      },
    },
  },
  {
    field: 'phone',
    title: '电话号码',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入电话号码',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入电话号码',
      },
    },
  },
  {
    field: 'idCard',
    title: '身份证号',
    width: '200',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入身份证号',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入电话号码',
      },
    },
  },
  {
    field: 'orgSetting',
    title: '所属组织',
    width: '460',
    slots: {
      default: 'orgSetting',
    },
  },
  {
    field: 'status',
    title: '状态',
    width: '80',
    slots: {
      default: 'statusSetting',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect',
      options: [
        { label: '启用', value: UserStatus.ACTIVE },
        { label: '作废', value: UserStatus.DEACTIVE },
        { label: '暂停', value: UserStatus.FREEZE },
      ],
    },
  },
  {
    field: 'roleSetting',
    title: '角色设置',
    width: '460',
    slots: {
      default: 'roleSetting',
    },
  },
  {
    field: 'operation',
    title: '操作列',
    minWidth: '200',
    slots: {
      default: 'operation',
    },
  },
];
// 表格所有配置
const tableOptions = reactive<any>({
  size: 'small',
  height: '100%',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: any) {
      // 编辑权限（忽略临时数据）
      if (!actionPermissions.apUpdate && !row._isTempData) {
        return false;
      }
      return true;
    },
  },
  editRules: {
    phone: [
      {
        required: true,
        message: '电话号码格式不正确!',
        pattern: phoneRegex,
      },
    ],
    idCard: [
      {
        required: false,
        message: '身份证格式不正确!',
        pattern: idCardRegex,
      },
    ],
  },
  rowConfig: {
    isCurrent: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'del',
            name: '删除',
            visible: true,
            disabled: !actionPermissions.apDelete,
          },
        ],
      ],
    },
  },
  columns,
  data: [],
});
// 表格grid全部事件
const gridEvents = {
  filterChange(data: any) {
    isFilter.value = data.filters.length === 0;
    const { filterList } = data;
    queryParams.page = 1;
    queryParams.nickname = '';
    queryParams.phone = '';
    queryParams.idCard = '';
    queryParams.status = '';
    filterList.forEach((item: any) => {
      queryParams[item.field as string] = item.datas[0];
    });
    getUserList();
  },
  // 表格确认修改
  async editClosed({ row }: any) {
    const isError = await orgTableRef.value.fullValidate(row);
    if (isError) return; // 存在错误直接跳出
    if (row.id) {
      // 修改操作
      const id = row.id;
      const params = {
        nickname: row.nickname,
        phone: row.phone,
        idCard: row.idCard,
      };
      const res = await userApi.updateUser(id, params);
      if (res) {
        ElMessage.success('修改成功');
        refreshTable();
      }
    } else {
      // 新增操作
      const params = {
        username: row.phone,
        nickname: row.nickname,
        phone: row.phone,
        idCard: row.idCard,

        orgIds: row.orgIdArr.join(','),
        roleIds: row.roleIdArr.join(','),
      };
      await addUser(params);
      isFinishInsert = true;
    }
  },
  cellMenu({ row }: { row: any }) {
    const $grid = orgTableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
  },
  menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'del') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        if (id) {
          const res = await userApi.deleteUser(id).catch((error) => {
            ElMessage.error(error.message || '删除失败 ');
            return false;
          });
          if (res) {
            refreshTable();
            ElMessage.success('删除成功');
          }
        } else {
          orgTableRef.value.remove(row);
          isFinishInsert = true;
        }
      });
    }
  },
};
const pageChange = () => {
  getUserList();
};
const filterHandle = () => {
  const $grid = orgTableRef.value;
  const filedMap = columns.filter((item) => !!item.filters).map((v) => v.field);
  if (isFilter.value) {
    // 关闭筛选
    $grid.openFilter('nickname');
  } else {
    // 关闭筛选
    filedMap.forEach((field) => {
      $grid.clearFilter(field);
    });
    $grid.closeFilter();
    queryParams.page = 1;
    queryParams.nickname = '';
    queryParams.phone = '';
    queryParams.idCard = '';
    queryParams.status = '';
    getUserList();
  }

  isFilter.value = !isFilter.value;
};
const showAllHandle = () => {
  showALlVisible.value = !showALlVisible.value;
  getUserList();
};

const selectUserHandle = () => {
  userDrawerVisible.value = true;
};

const addUserHandle = () => {
  const customerRow = {
    _isTempData: true,
    username: '',
    password: '',
    roleIds: '',
    orgIds: curTreeItem.value.id,
    orgIdArr: curTreeItem.value.id ? [curTreeItem.value.id] : [],
    roleIdArr: [],
    email: '',
    phone: '',
    nickname: '',
    idCard: '',
    status: UserStatus.ACTIVE,
  };
  nextTick(() => {
    if (isFinishInsert) {
      orgTableRef.value && orgTableRef.value.insertAt(customerRow);
      isFinishInsert = false;
    }
  });
};

const fileEl = ref();
const dcoxFile = ref<File>();
const importExcel = async () => {
  fileEl.value.value = ''; // 清空文件选择
  dcoxFile.value = undefined; // 清空文件选择
  fileEl.value.click();
};
const handleFileChange = async () => {
  const file = fileEl.value.files[0];
  if (!file) return;
  const orgId = userDrawerForm.orgIds;
  const res = await userApi.importUser(file, orgId);

  const errorMesg = res.results
    .filter((v: any) => v.success === false)
    .map((v: any) => v.error);
  if (errorMesg.length > 0) {
    ElMessage.error(errorMesg.join(' | '));
  }
  getUserList();
};
const exportExcel = async () => {
  const params: any = {
    page: queryParams.page,
    pageSize: queryParams.pageSize,
    nickname: queryParams.nickname,
    idCard: queryParams.idCard,
    phone: queryParams.phone,
    status: queryParams.status,
  };
  if (!showALlVisible.value) {
    params.orgIds = queryParams.orgIds;
  }

  try {
    const res = await userApi.exportUser(params);
    downloadFileFromBlobPart({
      fileName: `组织用户.xlsx`,
      source: res,
    });
  } catch {
    // 错误处理
  }
};

const downloadExcelTemplate = async () => {
  downloadLocalFile('/file/组织用户.xlsx', '组织用户模板.xlsx');
};

// 切换用户组织
const changeUserOrg = async (row: UserItem) => {
  const { id } = row;
  if (id) {
    const res = await userApi.updateUserOrg(id, {
      orgIds: row.orgIdArr.join(','),
    });
    if (res) {
      ElMessage.success('修改成功');
    }
  } else {
    const params = {
      username: row.phone,
      nickname: row.nickname,
      phone: row.phone,
      idCard: row.idCard,

      orgIds: row.orgIdArr.join(','),
      roleIds: row.roleIdArr.join(','),
    };
    addUser(params);
  }
};
// 切换用户角色
const changeUserRole = async (row: UserItem, cur: string, type: string) => {
  const { id, roleIdArr } = row;
  const roleIds = roleIdArr.join(',');
  const params = {
    userId: id,
    roleId: cur,
    addOrDel: type,
  };
  const consumActive = await consumAciveCode(params).catch(() => {
    getUserList();
  });
  if (!consumActive) {
    return;
  }

  if (id) {
    const res = await userApi.updateUserRole(id, {
      roleIds,
    });
    if (res) {
      ElMessage.success('修改成功');
    }
  } else {
    const params = {
      username: row.phone,
      nickname: row.nickname,
      phone: row.phone,
      idCard: row.idCard,

      orgIds: row.orgIdArr.join(','),
      roleIds: row.roleIdArr.join(','),
    };
    addUser(params);
  }
};
// 切换用户状态
const changeUserStatus = async (row: UserItem) => {
  const { id, status, nickname } = row;
  if (id) {
    ElMessageBox.confirm(
      `你确定要 ${getStatusLabel(status)} ${nickname} 吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      },
    )
      .then(async () => {
        const res = await userApi.boolUser(id, {
          filed: 'status',
          value: status,
        });
        if (res) {
          ElMessage.success('修改成功');
        }
        refreshTable();
      })
      .catch(() => {
        refreshTable();
      });
  } else {
    const params = {
      username: row.phone,
      nickname: row.nickname,
      phone: row.phone,
      idCard: row.idCard,

      orgIds: row.orgIdArr.join(','),
      roleIds: row.roleIdArr.join(','),
    };
    addUser(params);
  }
};

//  ------ Tree数据 ------
const treeDefaultSetting = ref({
  value: 'id',
  label: 'name',
  disabled: (v: any) => !v.isDirect,
  children: 'children',
});
const treeRef = ref<TreeInstance>();
const curTreeItem = ref<TreeNode>({
  // 当前左键选中的树的数据
  id: '',
  name: '',
  type: OrgType.COMPANY,
});
const curContextMenuTreeItem = ref<TreeNode>({
  // 当前右键选中的树的数据
  id: '',
  name: '',
  type: OrgType.COMPANY,
});
const expandIdx = ref(0); // 展开层级下标
const filterText = ref(''); // 筛选数据
const defaultExpendKeys = ref<string[]>([]);
const treeData = ref([]);
watch(filterText, (val) => {
  treeRef.value!.filter(val);
});
const filterNode = (value: string, data: any) => {
  // 筛选方法
  if (!value) return true;
  return data.name.includes(value);
};
const expandClick = (level: number) => {
  // 展开前提前关闭所有节点
  if (!treeRef.value) return;
  const nodeDatas: TreeStoreNodesMap = treeRef.value.store.nodesMap;

  for (const key in nodeDatas) {
    if (nodeDatas[key]) {
      nodeDatas[key].expanded = false;
    }
  }
  // 点击展开方法
  defaultExpendKeys.value = getIdsByLevel(treeData.value, level);
  expandIdx.value = level;
};
const treeNodeClick = (data: TreeNode) => {
  addBtnDisabled.value = false;
  showALlVisible.value = false;
  queryParams.orgIds = data.id;

  userDrawerForm.orgIds = data.id;

  curTreeItem.value = data;
  getUserList();
};

//  ------ 弹窗表单数据 ------
const orgFromDialogVisible = ref(false);
const editForm = reactive<editForm>({
  id: '',
  name: '',
  authCode: '',
  sealName: '',
  type: '',
  parentId: '',
  isHide: '',
  sort: '',
});

//  ------ 用户抽屉数据 ------
const userDrawerVisible = ref(false);
const userDrawerForm = reactive<userDrawerForm>({
  orgIds: '',
});
//  ------ Tree右键菜单数据 ------
const contextMenu = ref();
const TREE_NODE_TYPE = ref<menuType[]>([
  {
    label: '新增本级公司',
    disabled: false,
    type: 'COMPANY_OF_THE_SAME_CLASS',
  },
  { label: '新增下级公司', disabled: false, type: 'SUBORDINATE_COMPANY' },
  { label: '新增项目', disabled: false, type: 'NEW_PROJECT' },
  { label: '编辑', disabled: false, type: 'EDIT' },
  { label: '删除', disabled: false, type: 'DELETE' },
  { label: '设为不可见', disabled: false, type: 'INVISIBLE' },
]);
const resetTreeNodeType = () => {
  TREE_NODE_TYPE.value = [
    {
      label: '新增本级公司',
      disabled: !actionPermissions.apCreate,
      type: 'COMPANY_OF_THE_SAME_CLASS',
    },
    {
      label: '新增下级公司',
      disabled: !actionPermissions.apCreate,
      type: 'SUBORDINATE_COMPANY',
    },
    {
      label: '新增项目',
      disabled: !actionPermissions.apCreate,
      type: 'NEW_PROJECT',
    },
    { label: '编辑', disabled: !actionPermissions.apUpdate, type: 'EDIT' },
    { label: '删除', disabled: !actionPermissions.apDelete, type: 'DELETE' },
    {
      label: '设为不可见',
      disabled: !actionPermissions.apUpdate,
      type: 'INVISIBLE',
    },
  ];
};
const updateMenuItem = (index: number, updates: Partial<menuType>) => {
  if (TREE_NODE_TYPE.value[index]) {
    Object.assign(TREE_NODE_TYPE.value[index], updates);
  }
};
const disableMenuItems = (indices: number[]) => {
  indices.forEach((index) => {
    if (TREE_NODE_TYPE.value[index]) {
      TREE_NODE_TYPE.value[index].disabled = true;
    }
  });
};
const updateTreeNodeType = (data: TreeNode) => {
  // 如果没有父节点
  if (!data.parentId) {
    disableMenuItems([0, 3, 4, 5]);
  }
  // 如果节点是隐藏状态
  if (data.isHide) {
    updateMenuItem(5, { label: '设为可见', type: 'VISIBLE' });
  }
  // 如果节点类型是项目
  if (data.type === OrgType.PROJECT) {
    disableMenuItems([1, 2]);
  }
};

const contextMenuClick = (evt: MouseEvent, data: TreeNode) => {
  // 初始化
  resetTreeNodeType(); // 初始化菜单项
  updateTreeNodeType(data); // 根据节点数据更新菜单项

  curContextMenuTreeItem.value = data;
  contextMenu.value.open(evt);
};
const contextMenuChoice = ({ type }: { type: string }) => {
  switch (type) {
    case TREE_NODE_TYPE.value[0]?.type: {
      editForm.id = null;
      editForm.name = '';
      editForm.sealName = '';
      editForm.parentId = curContextMenuTreeItem.value.parentId;
      editForm.sort = '';
      editForm.authCode = '';
      editForm.type = OrgType.COMPANY;

      orgFromDialogVisible.value = true;
      break;
    }
    case TREE_NODE_TYPE.value[1]?.type: {
      editForm.id = null;
      editForm.name = '';
      editForm.sealName = '';
      editForm.parentId = curContextMenuTreeItem.value.id;
      editForm.sort = '';
      editForm.authCode = '';
      editForm.type = OrgType.COMPANY;

      orgFromDialogVisible.value = true;
      break;
    }
    case TREE_NODE_TYPE.value[2]?.type: {
      editForm.id = null;
      editForm.name = '';
      editForm.sealName = '';
      editForm.parentId = curContextMenuTreeItem.value.id;
      editForm.sort = '';
      editForm.authCode = '';
      editForm.type = OrgType.PROJECT;

      orgFromDialogVisible.value = true;
      break;
    }
    case TREE_NODE_TYPE.value[3]?.type: {
      editForm.id = curContextMenuTreeItem.value.id;
      editForm.name = curContextMenuTreeItem.value.name;
      editForm.sealName = curContextMenuTreeItem.value.sealName;
      editForm.parentId = curContextMenuTreeItem.value.parentId;
      editForm.sort = curContextMenuTreeItem.value.sort;
      editForm.authCode = curContextMenuTreeItem.value.authCode;
      editForm.type = curContextMenuTreeItem.value.type;

      orgFromDialogVisible.value = true;
      break;
    }
    case TREE_NODE_TYPE.value[4]?.type: {
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delOrg(curContextMenuTreeItem.value.id);
        if (res) {
          ElMessage.success('删除成功');
          refreshTree();
        }
      });

      break;
    }
    case TREE_NODE_TYPE.value[5]?.type: {
      ElMessageBox.confirm(
        `确定要将当前单位${TREE_NODE_TYPE.value[5]?.label}吗?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      ).then(async () => {
        const params = {
          filed: 'isHide',
          value: TREE_NODE_TYPE.value[5]?.type !== 'VISIBLE',
        };
        const res = await boolOrg(
          curContextMenuTreeItem.value.id,
          params,
        ).catch((error) => {
          ElMessage.error(error.message || '修改失败 ');
          return false;
        });
        if (res) {
          ElMessage.success('修改成功');
          refreshTree();
        }
      });
      break;
    }

    default: {
      break;
    }
  }
};

// 刷新树
const refreshTree = () => {
  getOrgList();
  commonStore.getOrganizationTreeData();
};
// 刷新用户列表
const refreshTable = () => {
  getUserList();
};

// ------ 接口数据 ------
// 获取组织树接口
async function getOrgList() {
  const tenantId = userStore.userInfo?.tenantId || '';
  const params = { tenantId };
  const res = await getOrganizationTree(params);
  calculateCount(res);
  treeData.value = res;
}
// 获取用户接口
async function getUserList() {
  tableLoading.value = true;
  const params: any = {
    page: queryParams.page,
    pageSize: queryParams.pageSize,
    nickname: queryParams.nickname,
    idCard: queryParams.idCard,
    phone: queryParams.phone,
    status: queryParams.status,
  };
  if (!showALlVisible.value) {
    params.orgIds = queryParams.orgIds;
  }
  const res = await userApi.getUserList(params);
  tableLoading.value = false;
  res.list.forEach((item: UserItem) => {
    item.roleIdArr = item.roles.map((v) => v.id);
    item.orgIdArr = item.orgs.map((v) => v.id);
  });
  tableOptions.data = res.list;
  tableTotal.value = res.pageInfo.total;
}
// 新增用户接口
async function addUser(params: any) {
  userApi.createUser(params).then(() => {
    ElMessage.success('新增成功');
    getUserList();
  });
}

async function init() {
  commonStore.getOrganizationTreeData();
  commonStore.getRoleTreeData();

  await getOrgList();
  await getUserList();
}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss">
.tree-content {
  height: calc(100vh - 180px);
  overflow: auto;
}

.status-group {
  .el-radio {
    margin-right: 10px;
  }
}
</style>
