export enum OrgType {
  COMPANY = 'COMPANY', // 公司
  PROJECT ='PROJECT', // 项目
  TENANT ='TENANT' // 租户根
}
export enum UserStatus {
  ACTIVE = 'ACTIVE', // 启用
  DEACTIVE = 'DEACTIVE', // 作废
  FREEZE = 'FREEZE', // 暂停
}

export interface BaseObject {
  [key: string]: any;
}

export interface TreeNode {
  id: string; // 节点唯一标识
  name: string; // 节点名称
  type: OrgType; // 节点类型
  isHide?: boolean; // 是否隐藏
  children?: TreeNode[]; // 子节点
  [key: string]: any; // 允许扩展其他属性
}
