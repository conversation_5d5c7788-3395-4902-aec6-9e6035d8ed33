<template>
  <ElDrawer
    v-bind="$attrs"
    class="customer-drawer"
    modal-class="customer-modal"
    v-model="dialogVisible"
    :append-to-body="true"
    title="选择用户"
    direction="rtl"
    size="40%"
    @closed="handleClose"
  >
    <VxeGrid
      ref="tableRef"
      :loading="tableLoading"
      v-bind="tableOptions"
      v-on="gridEvents"
    >
      <template #top>
        <div class="flex items-center justify-between">
          <div class="mb-2">
            <ElButton
              type="primary"
              size="small"
              :disabled="!actionPermissions.apCreate || !addUserPass"
              @click="addUserHandle"
            >
              添加用户
            </ElButton>
            <ElButton size="small" @click="filterHandle">
              <div>{{ isFilter ? '筛选' : '取消筛选' }}</div>
            </ElButton>
          </div>
        </div>
      </template>
      <template #orgSetting="{ row }">
        <OrgSelector v-model="row.orgIdArr" :multiple="true" :disabled="true" />
      </template>
      <template #roleSetting="{ row }">
        <RoleSelector v-model="row.roleIdArr" :disabled="true" />
      </template>
      <template #statusSetting="{ row }">
        <div>
          <ElTag
            :type="
              row.status === UserStatus.ACTIVE
                ? 'success'
                : row.status === UserStatus.DEACTIVE
                  ? 'danger'
                  : 'primary'
            "
          >
            {{ getStatusLabel(row.status) }}
          </ElTag>
        </div>
      </template>
    </VxeGrid>
    <vxe-pager
      size="mini"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.pageSize"
      :total="tableTotal"
      @page-change="pageChange"
    />
  </ElDrawer>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive, ref, watch } from 'vue';

import { ElButton, ElDrawer, ElMessage, ElTag } from 'element-plus';

import userApi, {
  updateUserOrg,
} from '#/api/systemManagementApi/userManagementApis';
import OrgSelector from '#/components/OrgSelector/index.vue';
import RoleSelector from '#/components/RoleSelector/index.vue';
import { getCurrentPremission } from '#/utils/permission';

import { UserStatus } from '../types';

const props = withDefaults(
  defineProps<{
    formData: any;
    visible: boolean;
  }>(),
  {
    visible: false,
    formData: () => ({}),
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const { actionPermissions } = getCurrentPremission();

const dialogVisible = ref(props.visible);

interface QueryParams {
  page: number;
  pageSize: number;
  orgIds: string;
  nickname: string;
  idCard: string;
  phone: string;
  status: string;

  [key: string]: any;
}
const queryParams = reactive<QueryParams>({
  page: 1,
  pageSize: 15,

  orgIds: '',
  nickname: '',
  idCard: '',
  phone: '',
  status: '',
});
const addUserPass = ref(false);
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      queryParams.orgIds = newVal.orgIds;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

// 表格数据
const isFilter = ref(true);
const tableRef = ref();
const tableLoading = ref(false);
const getStatusLabel = (status: UserStatus) => {
  const statusMap = {
    [UserStatus.ACTIVE]: '启用',
    [UserStatus.DEACTIVE]: '作废',
    [UserStatus.FREEZE]: '暂停',
  };
  return statusMap[status] || '未知状态';
};
const tableTotal = ref(0);

const columns = [
  { type: 'checkbox', width: 60 },
  {
    type: 'seq',
    width: '60',
  },
  {
    field: 'nickname',
    title: '用户姓名',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入用户姓名',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入姓名',
      },
    },
  },
  {
    field: 'phone',
    title: '电话号码',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入电话号码',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入电话号码',
      },
    },
  },
  {
    field: 'idCard',
    title: '身份证号',
    width: '200',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入身份证号',
      },
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入电话号码',
      },
    },
  },
  {
    field: 'orgSetting',
    title: '所属组织',
    width: '420',
    slots: {
      default: 'orgSetting',
    },
  },
  {
    field: 'status',
    title: '状态',
    width: '80',
    slots: {
      default: 'statusSetting',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect',
      options: [
        { label: '启用', value: UserStatus.ACTIVE },
        { label: '作废', value: UserStatus.DEACTIVE },
        { label: '暂停', value: UserStatus.FREEZE },
      ],
    },
  },
  {
    field: 'roleSetting',
    title: '角色设置',
    width: '460',
    slots: {
      default: 'roleSetting',
    },
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: 800,
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  columns,
  data: [],
});
// 表格grid全部事件
const gridEvents = {
  filterChange(data: any) {
    isFilter.value = data.filters.length === 0;
    const { filterList } = data;
    queryParams.page = 1;
    queryParams.nickname = '';
    queryParams.phone = '';
    queryParams.idCard = '';
    queryParams.status = '';
    filterList.forEach((item: any) => {
      queryParams[item.field as string] = item.datas[0];
    });
    getUserList();
  },
  checkboxAll() {
    const records = tableRef.value.getCheckboxRecords();
    addUserPass.value = !(records.length <= 0);
  },
  checkboxChange() {
    const records = tableRef.value.getCheckboxRecords();
    addUserPass.value = !(records.length <= 0);
  },
};
const pageChange = () => {
  getUserList();
};
const addUserHandle = async () => {
  // 转移用户
  const records = tableRef.value.getCheckboxRecords();
  if (records.length <= 0) return;

  // 构造所有异步请求的 Promise
  const promises = records.map(async (record: any) => {
    const { id, orgIdArr } = record;
    const orgIds = queryParams.orgIds;
    orgIdArr.push(orgIds);
    const ids = orgIdArr.join(',');

    // 调用接口更新用户组织
    const res = await updateUserOrg(id, { orgIds: ids });
    if (res) {
      const idx = tableOptions.data.findIndex((item: any) => item.id === id);
      if (idx !== -1) {
        tableOptions.data.splice(idx, 1); // 移除已处理的用户
      }
    }
    return res; // 返回结果
  });
  // 使用 Promise.all 等待所有请求完成
  const results = await Promise.all(promises);
  // 检查是否所有请求都成功
  if (results.every(Boolean)) {
    ElMessage.success('所有用户修改成功');
  } else {
    ElMessage.warning('部分用户修改失败');
  }

  emit('refresh');
};

const filterHandle = () => {
  const $grid = tableRef.value;
  const filedMap = columns.filter((item) => !!item.filters).map((v) => v.field);
  if (isFilter.value) {
    // 关闭筛选

    $grid.openFilter('nickname');
  } else {
    // 关闭筛选
    filedMap.forEach((field) => {
      $grid.clearFilter(field);
    });
    $grid.closeFilter();
    queryParams.page = 1;
    queryParams.nickname = '';
    queryParams.phone = '';
    queryParams.idCard = '';
    queryParams.status = '';
    getUserList();
  }

  isFilter.value = !isFilter.value;
};

async function getUserList() {
  tableLoading.value = true;
  const params: any = {
    page: queryParams.page,
    pageSize: queryParams.pageSize,
    nickname: queryParams.nickname,
    idCard: queryParams.idCard,
    phone: queryParams.phone,
    orgIds: queryParams.orgIds,
    status: queryParams.status,
  };
  const res = await userApi.getNotOrgUserList(params);
  tableLoading.value = false;
  res.list.forEach((item: any) => {
    item.roleIdArr = item.roles.map((v: any) => v.id);
    item.orgIdArr = item.orgs.map((v: any) => v.id);
  });
  tableOptions.data = res.list;
  tableTotal.value = res.pageInfo.total;
}
function handleClose() {
  emit('update:visible', false);
}

onBeforeMount(async () => {
  await getUserList();
});
</script>

<style lang="scss">
// .customer-drawer {
//   width: 100% !important;
// }
.customer-modal {
  // width: 40% !important;
  // max-width: 1200px;
  // min-width: 600px;
  // margin-left: auto;
  z-index: 600 !important;
}
</style>
