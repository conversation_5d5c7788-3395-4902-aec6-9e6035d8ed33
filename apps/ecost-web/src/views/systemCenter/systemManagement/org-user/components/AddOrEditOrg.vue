<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    :destroy-on-close="false"
    :width="600"
    :title="orgForm?.id ? `编辑${title}` : `新增${title}`"
    @close="handleClose"
    top="4%"
  >
    <ElForm
      :model="orgForm"
      ref="orgFormRef"
      :rules="formRules"
      :disabled="
        orgForm?.id ? !actionPermissions.apUpdate : !actionPermissions.apCreate
      "
      label-width="160px"
    >
      <div>
        <ElFormItem :label="`${title}简称`" prop="name">
          <ElInput v-model="orgForm.name" />
        </ElFormItem>
        <ElFormItem :label="`${title}合同章名称`" prop="sealName">
          <ElInput v-model="orgForm.sealName" :disabled="isEdit" />
        </ElFormItem>
        <ElFormItem label="上级组织">
          <OrgSelector
            v-model="orgForm.parentId"
            :disabled="isEdit"
            :disabled-props="disabledProps"
          />
        </ElFormItem>
        <ElFormItem label="排序号">
          <ElInput v-model="orgForm.sort" />
        </ElFormItem>
        <ElFormItem label="授权码">
          <ElInput
            v-model="orgForm.authCode"
            :disabled="hasAuthCode"
            @blur="blurAuthCode"
          />
        </ElFormItem>
      </div>

      <div>
        <ElDivider />
        <div class="title mb-4 text-lg text-[#303133]">填写激活码</div>

        <ElFormItem v-for="item in codeForm" :key="item.id" :label="item.name">
          <ElInput v-model="item.value" :disabled="hasActiveCode" />
        </ElFormItem>
      </div>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton
        v-auth="
          orgForm?.id ? actionPermissions.apUpdate : actionPermissions.apCreate
        "
        type="primary"
        @click="handleSubmit"
      >
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import { computed, onBeforeMount, ref, watch } from 'vue';

import {
  ElButton,
  ElDialog,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
} from 'element-plus';

import {
  activeActiveCode,
  getActiveCodeByOrg,
} from '#/api/authorization/author';
import { checkAuthCode } from '#/api/systemManagementApi/customer';
import { addOrg, editOrg } from '#/api/systemManagementApi/organizationApis';
import OrgSelector from '#/components/OrgSelector/index.vue';
import { useCommonStore } from '#/store';
import { getCurrentPremission } from '#/utils/permission';

import { OrgType } from '../types';

const props = withDefaults(
  defineProps<{
    formData: any;
    visible: boolean;
  }>(),
  {
    visible: false,
    formData: () => ({}),
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const { actionPermissions } = getCurrentPremission();

const title = computed(() => {
  return props.formData.type === OrgType.PROJECT ? '项目' : '公司';
});

const disabledProps = computed(() => {
  return OrgType.PROJECT;
});

const isEdit = computed(() => {
  return !!props.formData.id;
});

const hasAuthCode = computed(() => {
  return !!props.formData.authCode && props.formData.authCode !== '';
});

const commonStore = useCommonStore();
const productList = commonStore.productData;

interface codeFormItem {
  value: string;
  name: string;
  id: string;
  code: string;
}
const codeForm = ref<codeFormItem[]>([]);

productList.forEach((item) => {
  const { name, id, code } = item;
  codeForm.value.push({
    value: '',
    name,
    id,
    code,
  });
});

const hasActiveCode = ref(false);

const orgForm = ref({
  id: '',
  name: '',
  parentId: '',
  sealName: '',
  sort: 0,
  authCode: '',
  type: '',
});

const formRules = ref({
  name: [
    {
      required: true,
      message: `请输入${title.value}名称`,
      trigger: ['blur', 'change'],
    },
  ],
  sealName: [
    {
      required: true,
      message: `请输入${title.value}合同章名称`,
      trigger: ['blur', 'change'],
    },
  ],
});
const orgFormRef = ref();

const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

const blurAuthCode = async () => {
  await checkAuthCode(orgForm.value.authCode);
};

function handleClose() {
  emit('update:visible', false);
}
const handleSubmit = async () => {
  orgFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 修改的情况
      if (orgForm.value.id && orgForm.value.id !== '') {
        const params = {
          name: orgForm.value.name,
          parentId: orgForm.value.parentId,
          sealName: orgForm.value.sealName,
          sort: orgForm.value.sort,
          authCode: orgForm.value.authCode,
        };

        let isPassAuthCode = true;
        if (orgForm.value.authCode !== '') {
          isPassAuthCode = await checkAuthCode(orgForm.value.authCode);
        }
        if (!isPassAuthCode) return;

        const res = await editOrg(orgForm.value.id, params);
        if (res) {
          // ElMessage.success('编辑成功');
          handleClose();
          emit('refresh');
        }

        const orgId = orgForm.value.id;
        const authCode = orgForm.value.authCode;
        const projectName = orgForm.value.name;
        const activateData = codeForm.value
          .filter((item) => {
            return item.value && item.value !== ''; // 过滤掉不符合条件的项
          })
          .map((item) => ({
            productId: item.id,
            activateCode: item.value,
          }));
        let isPass = false;
        if (orgForm.value.type === OrgType.COMPANY) {
          isPass = authCode !== '' && activateData.length > 0;
        }
        if (orgForm.value.type === OrgType.PROJECT) {
          isPass = activateData.length > 0;
        }

        if (isPass) {
          const codeRes = await activeActiveCode({
            orgId,
            code: authCode,
            projectName,
            activateData,
          });
          if (codeRes.length > 0) {
            const successRes = codeRes
              .filter((v: any) => v.code !== 1)
              .map((item: any) => item.message);
            const errorRes = codeRes
              .filter((v: any) => v.code === 1)
              .map((item: any) => item.message);
            if (successRes.length > 0) {
              ElMessage.success(successRes.join(' | '));
            }
            if (errorRes.length > 0) {
              ElMessage.error({
                message: errorRes.join(' | '),
                duration: 5000,
              });
            }
            handleClose();
            emit('refresh');
          }
        }
      } else {
        let isPassAuthCode = true;
        if (orgForm.value.authCode !== '') {
          isPassAuthCode = await checkAuthCode(orgForm.value.authCode);
        }
        if (!isPassAuthCode) return;

        // 新增的情况
        const org = await addOrg({
          name: orgForm.value.name,
          parentId: orgForm.value.parentId,
          sealName: orgForm.value.sealName,
          sort: orgForm.value.sort,
          type: orgForm.value.type,
          authCode: orgForm.value.authCode,
        });
        if (org) {
          ElMessage.success('创建成功');
          handleClose();
          emit('refresh');
        }

        const orgId = org.id;
        const authCode = org.authCode;
        const projectName = org.name;
        const activateData = codeForm.value
          .filter((item) => {
            return item.value && item.value !== ''; // 过滤掉不符合条件的项
          })
          .map((item) => ({
            productId: item.id,
            activateCode: item.value,
          }));

        let isPass = false;
        if (orgForm.value.type === OrgType.COMPANY) {
          isPass = authCode !== '' && activateData.length > 0;
        }
        if (orgForm.value.type === OrgType.PROJECT) {
          isPass = activateData.length > 0;
        }
        if (isPass) {
          const codeRes = await activeActiveCode({
            orgId,
            code: authCode,
            projectName,
            activateData,
          });
          if (codeRes.length > 0) {
            const successRes = codeRes
              .filter((v: any) => v.code !== 1)
              .map((item: any) => item.message);
            const errorRes = codeRes
              .filter((v: any) => v.code === 1)
              .map((item: any) => item.message);
            if (successRes.length > 0) {
              ElMessage.success(successRes.join(' | '));
            }
            if (errorRes.length > 0) {
              ElMessage.error({
                message: errorRes.join(' | '),
                duration: 5000,
              });
            }
            handleClose();
            emit('refresh');
          }
        }
      }
    }
  });
};
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      orgForm.value = { ...newVal };
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

onBeforeMount(async () => {
  if (isEdit.value) {
    // 回显数据
    const res = await getActiveCodeByOrg(props.formData.id);
    if (res && res.length > 0) {
      codeForm.value.forEach((item) => {
        const product = res.find((v: any) => v.productId === item.id);
        if (product?.code && product?.code !== '') {
          hasActiveCode.value = true;
          item.value = product.code;
        }
      });
    }
  }
});
</script>
<style scoped lang="scss"></style>
