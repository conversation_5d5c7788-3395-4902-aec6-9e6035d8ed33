<template>
  <ColPage v-bind="colPageProps">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary" size="small">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <div
        :style="{ minWidth: '200px' }"
        v-else
        class="bg-card h-full rounded-lg border p-2"
      >
        <ElRadioGroup size="small" v-model="showTreeStatus">
          <ElRadioButton value="orgRole">组织角色</ElRadioButton>
          <ElRadioButton value="user">用户列表</ElRadioButton>
        </ElRadioGroup>
        <!-- 角色树区域 -->
        <div v-if="showTreeStatus === 'orgRole'">
          <div class="mt-2 flex items-center justify-between">
            <TreeLevelExpand
              :expand-idx="expandIdx"
              @expand-click="expandClick"
            />
            <ElInput
              class="ml-2"
              size="small"
              v-model="treeSearch"
              placeholder="请输入内容"
            />
          </div>
          <ElScrollbar height="750px">
            <ContextMenu
              ref="contextMenuRef"
              @select="handleNodeMenuClick"
              :menu="NodeMenu"
            >
              <ElTree
                size="small"
                ref="treeRef"
                style="max-width: 600px"
                class="filter-tree"
                :default-expanded-keys="defaultExpendKeys"
                :data="orgRoleTreeData"
                default-expand-all
                accordion
                :expand-on-click-node="false"
                :props="treeDefaultProps"
                :filter-node-method="filterNode"
                highlight-current
                node-key="id"
                @node-click="handleNodeClick"
                @node-contextmenu="treeNodeMenuClick"
              />
            </ContextMenu>
          </ElScrollbar>
        </div>
        <!-- 用户列表区域 -->
        <div v-if="showTreeStatus === 'user'">
          <div class="mb-2 mt-2 flex items-center justify-between">
            <ElButton size="small" type="primary" class="mr-2">筛选</ElButton>
            <ElInput
              size="small"
              v-model="userNameSearch"
              placeholder="请输入内容"
              clearable
              @keyup.enter="onUserNameSearch"
              @clear="onUserNameSearchClear"
            />
          </div>
          <!-- 用户列表 -->
          <VxeGrid
            v-bind="userTableOptions"
            @cell-click="handleUserTableClick"
            ref="userTableRef"
          >
            <template #userOrgSlot="{ row }">
              {{ row.orgs.map((item: any) => item.name).join(',') }}
            </template>
          </VxeGrid>
        </div>
      </div>
    </template>
    <div class="bg-card ml-2 h-full rounded-lg border p-2">
      <div v-if="showTreeStatus === 'orgRole'">
        <ElRadioGroup
          size="small"
          v-model="showTableStatus"
          class="mb-2"
          @change="handleRadioGroupChange"
        >
          <ElRadioButton value="actionPermission" :disabled="isDisabledFn">
            操作权限
          </ElRadioButton>
          <ElRadioButton value="fieldPermission" :disabled="isDisabledFn">
            字段权限
          </ElRadioButton>
          <ElRadioButton value="rolePermission" :disabled="isDisabledFn">
            角色成员
          </ElRadioButton>
        </ElRadioGroup>
        <!-- 操作权限区域 -->
        <div v-if="showTableStatus === 'actionPermission'">
          <VxeGrid
            v-bind="actionPermissionTableOptions"
            ref="actionPermissionTableRef"
          >
            <template #apCreateSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apCreate"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apCreate', $event)
                "
              />
            </template>
            <template #apDeleteSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apDelete"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apDelete', $event)
                "
              />
            </template>
            <template #apUpdateSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apUpdate"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apUpdate', $event)
                "
              />
            </template>
            <template #apExportSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apExport"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apExport', $event)
                "
              />
            </template>
            <template #apImportSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apImport"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apImport', $event)
                "
              />
            </template>
            <template #apPrintSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apPrint"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apPrint', $event)
                "
              />
            </template>
            <!-- 查看 -->
            <template #viewActionSlot="{ row }">
              <ElRadioGroup
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apView"
                size="small"
                @change="
                  handleActionPermissionCheckboxChange(row, 'apView', $event)
                "
              >
                <ElRadio
                  size="small"
                  :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_EVERYONE"
                >
                  所有人
                </ElRadio>
                <ElRadio
                  size="small"
                  :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_SELF"
                  :disabled="!row.canViewSelf"
                >
                  仅自己
                </ElRadio>
                <ElRadio
                  size="small"
                  :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_NONE"
                >
                  无查看权限
                </ElRadio>
              </ElRadioGroup>
            </template>
            <!-- 审核 -->
            <template #examineSlot="{ row }">
              <ElCheckbox
                :disabled="!actionPermissions.apUpdate || row.isBizAdmin"
                v-model="row.apExamine"
              />
            </template>
          </VxeGrid>
        </div>
        <!-- 字段权限区域 -->
        <div v-else-if="showTableStatus === 'fieldPermission'">
          <VxeGrid
            v-bind="fieldPermissionTableOptions"
            ref="fieldPermissionTableRef"
          >
            <template #filedEditSlot="{ row }">
              <ElCheckbox
                v-model="row.fpEdit"
                :disabled="!actionPermissions.apUpdate || row.isDisable"
                @change="
                  handleFieldPermissionCheckboxChange(row, 'fpEdit', $event)
                "
              />
            </template>
            <template #filedViewSlot="{ row }">
              <ElCheckbox
                v-model="row.fpView"
                :disabled="!actionPermissions.apUpdate || row.isDisable"
                @change="
                  handleFieldPermissionCheckboxChange(row, 'fpView', $event)
                "
              />
            </template>
          </VxeGrid>
        </div>
        <!-- 角色权限区域 -->
        <div v-else>
          <VxeGrid
            v-bind="rolePermissionTableOptions"
            ref="rolePermissionTableRef"
          >
            <template #top>
              <div class="mb-2 flex items-center">
                <ElButton
                  v-auth="actionPermissions.apCreate"
                  size="small"
                  type="primary"
                  @click="selectUser"
                >
                  选择用户
                </ElButton>
                <ElInput
                  size="small"
                  class="ml-2"
                  style="width: 300px"
                  v-model="nickname"
                  placeholder="请输入用户名称"
                  clearable
                  @clear="handleClearNicinameSearch"
                  @keyup.enter="onSearch"
                />
              </div>
            </template>
            <template #userOrgSlot="{ row }">
              {{ row.orgs.map((item: any) => item.name).join(',') }}
            </template>
            <template #roleSettingSlot="{ row }">
              <ElTag
                v-for="item in row.roles"
                :key="item.id"
                size="small"
                class="mr-2"
                plain
                :closable="actionPermissions.apUpdate"
                @close="handleRoleTagClose(row, item)"
              >
                {{ item.name }}
              </ElTag>
            </template>
          </VxeGrid>
        </div>
      </div>
      <div v-else-if="showTreeStatus === 'user'">
        <div class="mb-2">
          <RoleSelector
            size="small"
            :collapse-tags="true"
            :max-collapse-tags="6"
            v-model="currentUserRoleId"
            :disabled="!actionPermissions.apUpdate"
            @change="handleSelectUserRole"
          />
        </div>
        <VxeGrid v-bind="actionRoleTableOptions" ref="actionRoleTableRef">
          <template #apCreateSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apCreateFromRole"
              v-model="row.apCreateFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apCreate"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apCreate', $event)"
            />
          </template>
          <template #apDeleteSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apDeleteFromRole"
              v-model="row.apDeleteFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apDelete"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apDelete', $event)"
            />
          </template>
          <template #apUpdateSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apUpdateFromRole"
              v-model="row.apUpdateFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apUpdate"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apUpdate', $event)"
            />
          </template>
          <template #apExportSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apExportFromRole"
              v-model="row.apExportFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apExport"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apExport', $event)"
            />
          </template>
          <template #apImportSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apImportFromRole"
              v-model="row.apImportFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apImport"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apImport', $event)"
            />
          </template>
          <template #apPrintSlot="{ row }">
            <ElCheckbox
              disabled
              v-if="row.apPrintFromRole"
              v-model="row.apPrintFromRole"
            />
            <ElCheckbox
              v-else
              v-model="row.apPrint"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apPrint', $event)"
            />
          </template>
          <!-- 查看 -->
          <template #viewActionSlot="{ row }">
            <ElRadioGroup
              v-if="row.apViewFromRole"
              v-model="row.apViewFromRole"
              disabled
            >
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_EVERYONE"
              >
                所有人
              </ElRadio>
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_SELF"
                :disabled="!row.canViewSelf"
              >
                仅自己
              </ElRadio>
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_NONE"
              >
                无查看权限
              </ElRadio>
            </ElRadioGroup>
            <ElRadioGroup
              v-else
              v-model="row.apView"
              :disabled="!actionPermissions.apUpdate"
              @change="handleUpdateUserPerCheckbox(row, 'apView', $event)"
            >
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_EVERYONE"
              >
                所有人
              </ElRadio>
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_SELF"
                :disabled="!row.canViewSelf"
              >
                仅自己
              </ElRadio>
              <ElRadio
                size="small"
                :value="ACTIONPERMISSIONVIEWCODE.AP_VIEW_NONE"
              >
                无查看权限
              </ElRadio>
            </ElRadioGroup>
          </template>
          <!-- 审核 -->
          <template #examineSlot>
            <ElCheckbox :disabled="!actionPermissions.apUpdate" />
          </template>
        </VxeGrid>
      </div>
    </div>
    <!-- 新增角色 -->
    <AddOrEditRole
      v-if="addOrEditRoleVisible"
      v-model:visible="addOrEditRoleVisible"
      :node-data="treeNodeData"
      @refresh="getRoleTree"
    />
    <!-- 选择用户 -->
    <SelectUser
      v-if="selectUserVisible"
      v-model:visible="selectUserVisible"
      :node-data="treeNodeData"
      @select-user="handleSelectUser"
    />
  </ColPage>
</template>

<script lang="ts" setup>
import type { TreeStoreNodesMap } from 'element-plus';

import { computed, nextTick, reactive, ref, watch } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElCheckbox,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElRadio,
  ElRadioButton,
  ElRadioGroup,
  ElScrollbar,
  ElTag,
  ElTooltip,
  ElTree,
} from 'element-plus';

import {
  authorizePermission,
  deleteRole,
  delUserRole,
  editRoleActionPer,
  editRoleFieldPer,
  roleActionPer,
  roleAll,
  roleFiledPer,
  roleSetUser,
  roleTree,
  upDateUserPermission,
  userList,
  userPermission,
  userRoleAll,
} from '#/api/systemManagementApi/rolePermApis';
import { updateUserRole } from '#/api/systemManagementApi/userManagementApis';
import ContextMenu from '#/components/ContextMenu/index.vue';
import RoleSelector from '#/components/RoleSelector/index.vue';
import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import { useCommonStore } from '#/store';
import { getIdsByLevel } from '#/utils/common';
import { getCurrentPremission } from '#/utils/permission';

import AddOrEditRole from './components/addOrEditRole.vue';
import SelectUser from './components/selectUser.vue';
import { ACTIONPERMISSIONVIEWCODE, colPageProps } from './data';

const { actionPermissions } = getCurrentPremission();

const actionRoleTableRef = ref();
const orgAppTreeNode = ref<any>({});
const selectUserVisible = ref(false);
const expandIdx = ref(0);
const defaultExpendKeys = ref<string[]>([]);
const treeNodeData = ref<any>({});
const addOrEditRoleVisible = ref(false);
const showTreeStatus = ref('orgRole');
const showTableStatus = ref('actionPermission');
const treeSearch = ref('');
const userNameSearch = ref('');
const nickname = ref('');
const treeRef = ref();
const treeDefaultProps = ref({
  children: 'children',
  label: 'name',
});
const contextMenuRef = ref();
const orgRoleTreeData = ref([]);
const currentUserTableRow = ref<any>({
  id: null,
});
const roleAllList = ref<any>([]);
const currentUserRoleId = ref<string[]>([]);
const userTableRef = ref();

const commonStore = useCommonStore();
commonStore.getRoleTreeData();

// 左侧用户列表配置
const userTableOptions = reactive<any>({
  size: 'mini',
  border: true,
  height: '100%',
  align: 'center',
  showOverflow: true,
  keepSource: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  headerCellConfig: {
    height: 40,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columns: [
    {
      align: 'left',
      type: 'seq',
      width: 50,
    },
    {
      field: 'nickname',
      title: '姓名',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'phone',
      title: '电话号码',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'idCard',
      title: '身份证号',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'userOrg',
      title: '所属组织',
      slots: {
        default: 'userOrgSlot',
      },
    },
  ],
  data: [],
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    layouts: ['Number', 'Sizes', 'Total'],
    size: 'mini',
  },
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async ({ page }: any, formValues: any) => {
        const res = await userList({
          page: page.currentPage,
          pageSize: page.pageSize,
          nickname: userNameSearch.value,
          ...formValues,
        });
        userTableOptions.data = res.list;
        return res;
      },
    },
  },
});
// 操作权限区域配置
const actionPermissionTableRef = ref();
const actionPermissionTableOptions = reactive<any>({
  size: 'mini',
  border: true,
  align: 'center',
  maxHeight: '750px',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  treeConfig: {
    rowField: 'menuId',
    parentField: 'menuParentId',
    transform: true,
    expandAll: true,
  },
  columns: [
    {
      align: 'center',
      type: 'seq',
      width: 50,
    },
    {
      width: 150,
      field: 'moduleName',
      title: '模块名称',
      treeNode: true,
    },
    {
      field: 'apCreate',
      title: '新增',
      slots: {
        default: 'apCreateSlot',
      },
    },
    {
      field: 'apDelete',
      title: '删除',
      slots: {
        default: 'apDeleteSlot',
      },
    },
    {
      field: 'apUpdate',
      title: '编辑',
      slots: {
        default: 'apUpdateSlot',
      },
    },
    {
      field: 'apExport',
      title: '导出',
      slots: {
        default: 'apExportSlot',
      },
    },
    {
      field: 'apImport',
      title: '导入',
      slots: {
        default: 'apImportSlot',
      },
    },
    {
      field: 'apPrint',
      title: '打印',
      slots: {
        default: 'apPrintSlot',
      },
    },
    {
      minWidth: 200,
      field: 'apView',
      title: '查看',
      slots: {
        default: 'viewActionSlot',
      },
    },
    // 审核功能没做，暂时隐藏
    // {
    //   field: 'apExamine',
    //   title: '审核',
    //   slots: {
    //     default: 'examineSlot',
    //   },
    // },
  ],
  data: [],
});
// 字段权限区域配置
const fieldPermissionTableRef = ref();
const fieldPermissionTableOptions = reactive<any>({
  size: 'mini',
  border: true,
  align: 'center',
  maxHeight: '750px',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  treeConfig: {
    rowField: 'menuId',
    parentField: 'menuParentId',
    transform: true,
    expandAll: true,
  },
  columns: [
    {
      width: 50,
      align: 'center',
      type: 'seq',
    },
    {
      field: 'moduleName',
      title: '模块名称',
      treeNode: true,
    },
    {
      field: 'moduleFieldName',
      title: '字段名称',
    },
    {
      width: 100,
      field: 'fpEdit',
      title: '编辑',
      slots: {
        default: 'filedEditSlot',
      },
    },
    {
      width: 100,
      field: 'fpView',
      title: '查看',
      slots: {
        default: 'filedViewSlot',
      },
    },
  ],
  data: [],
});
const rolePermissionTableRef = ref();
// 用户角色权限区域配置
const rolePermissionTableOptions = reactive<any>({
  size: 'mini',
  border: true,
  maxHeight: '750px',
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columns: [
    {
      type: 'seq',
      width: 50,
    },
    {
      width: 100,
      field: 'nickname',
      title: '用户姓名',
    },
    {
      width: 100,
      field: 'phone',
      title: '电话号码',
    },
    {
      width: 130,
      field: 'idCard',
      title: '身份证号',
    },
    {
      width: 220,
      field: 'userOrg',
      title: '所属组织',
      slots: {
        default: 'userOrgSlot',
      },
    },
    {
      align: 'center',
      field: 'userRole',
      title: '角色设置',
      slots: {
        default: 'roleSettingSlot',
      },
    },
  ],
  data: [],
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    layouts: ['PrevJump', 'Number', 'NextJump', 'Sizes', 'Total'],
    size: 'mini',
  },
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async ({ page }: any, formValues: any) => {
        return await userList({
          page: page.currentPage,
          pageSize: page.pageSize,
          roleIds: treeNodeData.value.id,
          nickname: nickname.value,
          ...formValues,
        });
      },
    },
  },
});
function onSearch() {
  rolePermissionTableRef.value.commitProxy('query');
}
function onUserNameSearch() {
  userTableRef.value.commitProxy('query');
}
function onUserNameSearchClear() {
  userNameSearch.value = '';
  userTableRef.value.commitProxy('query');
}
function handleClearNicinameSearch() {
  nickname.value = '';
  rolePermissionTableRef.value &&
    rolePermissionTableRef.value.commitProxy('query');
}
// 用户角色选择配置
const actionRoleTableOptions = reactive<any>({
  size: 'mini',
  border: true,
  align: 'center',
  maxHeight: '750px',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  treeConfig: {
    rowField: 'menuId',
    parentField: 'menuParentId',
    transform: true,
    expandAll: true,
  },
  columns: [
    {
      align: 'left',
      type: 'seq',
      width: 50,
    },
    {
      minWidth: 150,
      field: 'moduleName',
      title: '模块名称',
      treeNode: true,
    },
    {
      field: 'apCreate',
      title: '新增',
      slots: {
        default: 'apCreateSlot',
      },
    },
    {
      field: 'apDelete',
      title: '删除',
      slots: {
        default: 'apDeleteSlot',
      },
    },
    {
      field: 'apUpdate',
      title: '编辑',
      slots: {
        default: 'apUpdateSlot',
      },
    },
    {
      field: 'apExport',
      title: '导出',
      slots: {
        default: 'apExportSlot',
      },
    },
    {
      field: 'apImport',
      title: '导入',
      slots: {
        default: 'apImportSlot',
      },
    },
    {
      field: 'apPrint',
      title: '打印',
      slots: {
        default: 'apPrintSlot',
      },
    },
    {
      field: 'apView',
      title: '查看',
      minWidth: 250,
      slots: {
        default: 'viewActionSlot',
      },
    },
    // 审核功能没做，暂时隐藏
    // {
    //   field: 'isConstructionCost',
    //   title: '审核',
    //   slots: {
    //     default: 'examineSlot',
    //   },
    // },
  ],
  data: [],
});
// 用户列表选中
function handleUserTableClick({ row }: { row: any }) {
  currentUserTableRow.value = row;
  getUserRoleAll(row.id);
}

// 树节点过滤
function filterNode(value: string, data: any) {
  if (!value) return true;
  return data.name.includes(value);
}
async function treeNodeMenuClick(event: any, node: any) {
  orgAppTreeNode.value = node;
  treeNodeData.value = node;
  contextMenuRef.value.open(event);
  // let productId = '';
  // let roleType = '';
  // if (node.type === 'role') {
  //   productId = data.parent.parent.data.id;
  //   roleType = data.parent.data.role_type;
  //   treeNodeData.value = {
  //     ...node,
  //     productId,
  //     roleType,
  //   };
  // } else if (node.type === 'roleCategory') {
  //   productId = data.parent.data.id;
  //   treeNodeData.value = {
  //     ...node,
  //     productId,
  //   };
  // }
}
// 节点点击
async function handleNodeClick(node: any) {
  orgAppTreeNode.value = node;
  treeNodeData.value = node;
  // let productId = '';
  // let roleType = '';
  // if (node.type === 'role') {
  //   productId = data.parent.parent.data.id;
  //   roleType = data.parent.data.role_type;
  //   treeNodeData.value = {
  //     ...node,
  //     productId,
  //     roleType,
  //   };
  // } else if (node.type === 'roleCategory') {
  //   productId = data.parent.data.id;
  //   treeNodeData.value = {
  //     ...node,
  //     productId,
  //   };
  // }
  // 角色层触发相关接口调用
  if (node.nodeType !== 'role') {
    actionPermissionTableOptions.data = [];
    fieldPermissionTableOptions.data = [];
    rolePermissionTableOptions.data = [];
    return;
  }
  // 获取相关操作权限
  switch (showTableStatus.value) {
    case 'actionPermission': {
      getActionPermission(node.id);
      break;
    }
    case 'fieldPermission': {
      getFieldPermission(node.id);
      break;
    }
    case 'rolePermission': {
      rolePermissionTableRef.value &&
        rolePermissionTableRef.value.commitProxy('query');
      break;
    }
  }
}
// 获取模块操作权限
async function getActionPermission(roleId: string) {
  // 获取角色下的模块列表权限
  actionPermissionTableOptions.data = await roleActionPer({
    roleId,
    tenantId: treeNodeData.value.tenantId,
    productId: treeNodeData.value.productId,
  });
  await nextTick(() => {
    actionPermissionTableRef.value &&
      actionPermissionTableRef.value.setAllTreeExpand(true);
  });
}
// 获取模块字段权限
async function getFieldPermission(roleId: string) {
  // 获取角色下的字段列表权限
  fieldPermissionTableOptions.data = await roleFiledPer({
    roleId,
    tenantId: treeNodeData.value.tenantId,
    productId: treeNodeData.value.productId,
  });
  await nextTick(() => {
    fieldPermissionTableRef.value &&
      fieldPermissionTableRef.value.setAllTreeExpand(true);
  });
}
// 顶部状态切换
function handleRadioGroupChange(value: any) {
  showTableStatus.value = value;
  switch (value) {
    case 'actionPermission': {
      getActionPermission(treeNodeData.value.id);
      break;
    }
    case 'fieldPermission': {
      getFieldPermission(treeNodeData.value.id);
      break;
    }
    case 'rolePermission': {
      rolePermissionTableRef.value &&
        rolePermissionTableRef.value.commitProxy('query');
      break;
    }
  }
}

// 右键菜单处理
function handleNodeMenuClick(item: { type: string }) {
  switch (item.type) {
    case 'CREATE': {
      addOrEditRoleVisible.value = true;
      break;
    }
    case 'DELETE': {
      ElMessageBox.confirm('确定删除该角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await deleteRole(treeNodeData.value.id);
        ElMessage.success('删除成功');
        await getRoleTree();
      });
      break;
    }
    case 'EDIT': {
      addOrEditRoleVisible.value = true;
      break;
    }
  }
}
// 根据当前节点状态动态设置右键菜单
const NodeMenu = computed(() => {
  return [
    {
      label: '新增角色',
      disabled:
        !actionPermissions.apCreate ||
        !(orgAppTreeNode.value?.nodeType === 'roleCategory'),
      type: 'CREATE',
    },
    {
      label: '编辑',
      disabled:
        !actionPermissions.apUpdate ||
        !(orgAppTreeNode.value?.nodeType === 'role') ||
        orgAppTreeNode.value?.isBizAdmin,
      type: 'EDIT',
    },
    {
      label: '删除',
      disabled:
        !actionPermissions.apDelete ||
        !(orgAppTreeNode.value?.nodeType === 'role') ||
        orgAppTreeNode.value?.isBizAdmin,
      type: 'DELETE',
    },
  ];
});
// 树节点展开
function expandClick(level: number) {
  // 展开前提前关闭所有节点
  if (!treeRef.value) return;
  const nodeDatas: TreeStoreNodesMap = treeRef.value.store.nodesMap;

  for (const key in nodeDatas) {
    if (nodeDatas[key]) {
      nodeDatas[key].expanded = false;
    }
  }
  // 点击展开方法
  defaultExpendKeys.value = getIdsByLevel(orgRoleTreeData.value, level);
  expandIdx.value = level;
}

// 获取用户所有角色
async function getUserRoleAll(id: string) {
  const userRoleList = await userRoleAll(id);
  currentUserRoleId.value =
    userRoleList && userRoleList.length > 0
      ? userRoleList.map((item: any) => item.id)
      : [];

  actionRoleTableOptions.data = await userPermission({
    userId: id,
    roleIds: currentUserRoleId.value,
  });
  await nextTick(() => {
    actionRoleTableRef.value && actionRoleTableRef.value.setAllTreeExpand(true);
  });
}
// 获取所有角色
async function getRoleAll() {
  roleAllList.value = await roleAll();
}

// 获取角色树
async function getRoleTree() {
  const productCode = sessionStorage.getItem('productCode') || null;
  if (productCode) {
    orgRoleTreeData.value = await roleTree(productCode);
  }
}
// 删除角色标签
async function handleRoleTagClose(row: any, item: any) {
  await delUserRole({
    userId: row.id,
    roleId: item.id,
  });
  rolePermissionTableRef.value &&
    rolePermissionTableRef.value.commitProxy('query');
}
// 选择用户
function selectUser() {
  selectUserVisible.value = true;
}

// 角色操作权限
async function handleActionPermissionCheckboxChange(
  row: { [key: string]: any; children: any[] },
  key: string,
  status: any,
) {
  // 构建权限数据
  const permissionData =
    row.children?.length > 0
      ? row.children.map((item: any) => ({
          ...item,
          [key]: status,
        }))
      : [
          {
            ...row,
            [key]: status,
          },
        ];

  await editRoleActionPer({
    data: permissionData,
    roleId: treeNodeData.value.id,
  });

  await getActionPermission(treeNodeData.value.id);
}
// 字段权限
async function handleFieldPermissionCheckboxChange(
  row: any,
  key: string,
  status: any,
) {
  // 构建字段权限数据
  const permissionData =
    row.children?.length > 0
      ? row.children.map((item: any) => ({
          ...item,
          [key]: status,
        }))
      : [
          {
            ...row,
            [key]: status,
          },
        ];

  await editRoleFieldPer({
    roleId: treeNodeData.value.id,
    data: permissionData,
  });
  await getFieldPermission(treeNodeData.value.id);
}
// // 递归获取所有子节点数据
// function getAllChildrenData(parentId: string, data: any[]): any[] {
//   const result: any[] = [];

//   // 查找直接子节点
//   const directChildren = data.filter((item) => item.menuParentId === parentId);

//   // 将直接子节点添加到结果中
//   result.push(...directChildren);

//   // 递归查找每个子节点的子节点
//   directChildren.forEach((child) => {
//     const grandChildren = getAllChildrenData(child.menuId, data);
//     result.push(...grandChildren);
//   });

//   return result;
// }

// 修改用户操作权限
async function handleUpdateUserPerCheckbox(row: any, key: string, status: any) {
  // 构建权限数据
  const permissionData =
    row.children?.length > 0
      ? row.children.map((item: any) => ({
          ...item,
          [key]: status,
        }))
      : [
          {
            ...row,
            [key]: status,
          },
        ];

  // 授权
  const res = await authorizePermission({
    userPermission: permissionData,
    userId: currentUserTableRow.value.id,
    addOrDel: status ? 'add' : 'del',
  });

  if (res) {
    await upDateUserPermission({
      userId: currentUserTableRow.value.id,
      data: permissionData,
    });
    await getUserRoleAll(currentUserTableRow.value.id);
  }
}

// 角色切换
async function handleSelectUserRole(val: string, type: string) {
  // 校验当前添加的角色是否有权限
  const roleId = val;
  const res = await authorizePermission({
    roleId,
    userId: currentUserTableRow.value.id,
    addOrDel: type,
  }).catch(() => {
    // 报错后恢复 默认
    currentUserRoleId.value = currentUserTableRow.value.roles.map(
      (v: any) => v.id,
    );
  });
  if (res) {
    // 通过校验后给用户挂角色
    const id = currentUserTableRow.value.id;
    const roleIds = currentUserRoleId.value?.join(',');
    const res = await updateUserRole(id, {
      roleIds,
    });
    if (res) {
      ElMessage.success('修改成功');
    }

    // 刷新用户列表 同时保持高亮
    await userTableRef.value.commitProxy('query');
    const row = userTableOptions.data.find(
      (item: any) => item.id === currentUserTableRow.value.id,
    );
    currentUserTableRow.value = row;
    currentUserRoleId.value = currentUserTableRow.value.roles.map(
      (item: any) => item.id,
    );
    nextTick(() => {
      userTableRef.value.setCurrentRow(row);
    });

    if (currentUserRoleId.value.length > 0) {
      actionRoleTableOptions.data = await userPermission({
        userId: currentUserTableRow.value.id,
        roleIds: currentUserRoleId.value,
      });
      await nextTick(() => {
        actionRoleTableRef.value &&
          actionRoleTableRef.value.setAllTreeExpand(true);
      });
    }
  }
}

const isDisabledFn = computed(() => {
  if (orgAppTreeNode.value?.nodeType === 'role') {
    return false;
  }
  return true;
});
async function handleSelectUser(userList: Array<any>) {
  if (userList.length > 0) {
    await roleSetUser({
      data: userList.map((item) => {
        return {
          userId: item.id,
          roleId: treeNodeData.value.id,
        };
      }),
    });
    rolePermissionTableRef.value &&
      rolePermissionTableRef.value.commitProxy('query');
  }
}

watch(treeSearch, (val) => {
  if (treeRef.value) {
    treeRef.value.filter(val);
  }
});

// 左侧顶部状态切换
watch(
  showTreeStatus,
  (val) => {
    if (val === 'orgRole') {
      getRoleTree();
    } else if (val === 'user') {
      getRoleAll();
      actionRoleTableOptions.data = [];
    }
  },
  {
    immediate: true,
  },
);
</script>

<style></style>
