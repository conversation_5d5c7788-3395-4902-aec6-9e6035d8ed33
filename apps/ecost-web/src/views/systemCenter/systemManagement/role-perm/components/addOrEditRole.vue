<template>
  <ElDialog
    v-bind="$attrs"
    v-model="dialogVisible"
    :destroy-on-close="false"
    :width="500"
    :title="roleForm?.id ? '编辑角色' : '新增角色'"
    @close="handleClose"
  >
    <ElForm
      :model="roleForm"
      ref="roleFormRef"
      :rules="roleFormRules"
      :disabled="
        roleForm?.id ? !actionPermissions.apUpdate : !actionPermissions.apCreate
      "
      label-width="100px"
    >
      <ElFormItem label="角色名称" prop="name">
        <ElInput v-model="roleForm.name" />
      </ElFormItem>
      <ElFormItem label="角色描述">
        <ElInput v-model="roleForm.description" />
      </ElFormItem>
      <ElFormItem label="排序">
        <ElInput v-model.number="roleForm.sort" type="number" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton
        v-auth="
          roleForm?.id ? actionPermissions.apUpdate : actionPermissions.apCreate
        "
        type="primary"
        @click="handleSubmit"
      >
        确定
      </ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';

import { ElButton, ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus';

import {
  addRole,
  editRole,
  roleInfo,
} from '#/api/systemManagementApi/rolePermApis';
import { getCurrentPremission } from '#/utils/permission';

const props = withDefaults(
  defineProps<{
    nodeData: any;
    visible: boolean;
  }>(),
  {
    visible: false,
    nodeData: () => ({}),
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

const { actionPermissions } = getCurrentPremission();

const roleForm = ref({
  id: null,
  name: '',
  description: '',
  sort: 0,
});
const roleFormRules = ref({
  name: [
    { required: true, message: '请输入角色名称', trigger: ['blur', 'change'] },
  ],
});
const roleFormRef = ref();
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
function handleClose() {
  emit('update:visible', false);
}
function handleSubmit() {
  roleFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (roleForm.value.id) {
        await editRole(roleForm.value.id, {
          name: roleForm.value.name,
          description: roleForm.value.description,
          sort: roleForm.value.sort,
        });
        handleClose();
        emit('refresh');
      } else {
        await addRole({
          productId: props.nodeData.productId,
          roleType: props.nodeData.roleType,
          name: roleForm.value.name,
          description: roleForm.value.description,
          sort: roleForm.value.sort,
        });
        handleClose();
        emit('refresh');
      }
    }
  });
}
watch(
  () => props.nodeData.id,
  async (newValue) => {
    if (newValue && props.nodeData.nodeType === 'role') {
      const res = await roleInfo(newValue);
      roleForm.value = res;
    }
  },
  {
    immediate: true,
  },
);
</script>
<style scoped lang="scss"></style>
