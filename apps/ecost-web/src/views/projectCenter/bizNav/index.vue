<template>
  <div class="excalidraw-container" :class="{ 'view-mode': isViewMode }">
    <div id="excalidraw" class="excalidraw-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Excalidraw } from '@excalidraw/excalidraw';
import React from 'react';
import { createRoot } from 'react-dom/client';

import { getBizNavElementList } from '#/api/core/module';

import '@excalidraw/excalidraw/index.css';

const route = useRoute();

// 字体目录
(window as any).EXCALIDRAW_ASSET_PATH = '/excalidraw-fonts';

let root: any = null;
const isViewMode = ref(true); // 默认是查看模式

const defaultAppState = {
  // 主题设置
  theme: 'light', // 主题模式：'light' | 'dark'

  currentChartType: 'bar', // 当前图表类型

  // 当前绘制工具的样式属性
  currentItemBackgroundColor: 'transparent', // 当前元素背景色
  currentItemEndArrowhead: 'arrow', // 当前元素结束箭头样式：'arrow' | null
  currentItemFillStyle: 'solid', // 当前元素填充样式：'solid' | 'hachure' | 'cross-hatch'
  currentItemFontFamily: 6, // 当前文本字体族（数字索引）
  currentItemFontSize: 20, // 当前文本字体大小
  currentItemOpacity: 100, // 当前元素透明度（0-100）
  currentItemRoughness: 0, // 当前元素粗糙度（0-2，0为最光滑）
  currentItemStartArrowhead: null, // 当前元素开始箭头样式：'arrow' | null
  currentItemStrokeColor: '#1e1e1e', // 当前元素描边颜色
  currentItemRoundness: 'sharp', // 当前元素圆角样式：'sharp' | 'round'
  currentItemArrowType: 'round', // 当前箭头类型：'round' | 'sharp'
  currentItemStrokeStyle: 'solid', // 当前元素描边样式：'solid' | 'dashed' | 'dotted'
  currentItemStrokeWidth: 2, // 当前元素描边宽度
  currentItemTextAlign: 'left', // 当前文本对齐方式：'left' | 'center' | 'right'

  // 鼠标状态
  cursorButton: 'up', // 鼠标按钮状态：'up' | 'down'

  // 编辑状态
  editingGroupId: null, // 当前编辑的组ID

  // 当前激活的工具
  activeTool: {
    type: 'selection', // 工具类型：'selection' | 'rectangle' | 'diamond' | 'ellipse' | 'arrow' | 'line' | 'freedraw' | 'text' | 'image' | 'eraser'
    customType: null, // 自定义工具类型
    locked: false, // 工具是否锁定
    fromSelection: false, // 是否从选择状态切换而来
    lastActiveTool: null, // 上一个激活的工具
  },

  // 手写笔支持
  penMode: false, // 是否启用手写笔模式
  penDetected: false, // 是否检测到手写笔

  // 导出设置
  exportBackground: true, // 导出时是否包含背景
  exportScale: 2, // 导出缩放比例
  exportEmbedScene: false, // 导出时是否嵌入场景数据
  exportWithDarkMode: false, // 导出时是否使用暗色模式

  // 网格设置
  gridSize: 20, // 网格大小
  gridStep: 5, // 网格步长
  gridModeEnabled: false, // 是否启用网格模式

  // 侧边栏设置
  defaultSidebarDockedPreference: false, // 默认侧边栏停靠偏好

  // 交互状态
  lastPointerDownWith: 'mouse', // 最后一次指针按下的设备类型：'mouse' | 'pen' | 'touch'

  // 画布名称
  name: '业务导航', // 画布名称，用于导出时的文件名

  // 菜单和侧边栏状态
  openMenu: null, // 当前打开的菜单
  openSidebar: null, // 当前打开的侧边栏

  // 选择状态
  previousSelectedElementIds: {}, // 之前选中的元素ID集合

  // 滚动状态
  scrolledOutside: false, // 是否滚动到画布外部
  scrollX: 0, // 水平滚动位置
  scrollY: 0, // 垂直滚动位置

  // 当前选择状态
  selectedElementIds: {}, // 当前选中的元素ID集合
  selectedGroupIds: {}, // 当前选中的组ID集合

  // 缓存设置
  shouldCacheIgnoreZoom: false, // 缓存时是否忽略缩放

  // 统计面板
  stats: { open: false, panels: 3 }, // 统计面板状态：是否打开和面板数量

  // 视图设置
  viewBackgroundColor: '#ffffff', // 视图背景颜色
  viewModeEnabled: true, // 是否启用查看模式（只读模式）
  zenModeEnabled: false, // 是否启用禅模式（隐藏UI元素）
  zoom: { value: 1 }, // 缩放级别

  // 线性元素选择
  selectedLinearElement: null, // 当前选中的线性元素

  // 对象吸附
  objectsSnapModeEnabled: false, // 是否启用对象吸附模式

  // 多选锁定
  lockedMultiSelections: {}, // 锁定的多选集合
};

onMounted(async () => {
  try {
    await nextTick();
    await initializeExcalidraw();
  } catch (error_) {
    console.error('Failed to initialize Excalidraw:', error_);
  }
});

onUnmounted(() => {
  // 清理右键菜单事件监听器
  const container = document.querySelector('#excalidraw');
  if (container && (container as any).__contextMenuCleanup) {
    (container as any).__contextMenuCleanup();
  }

  if (root) {
    try {
      root.unmount();
      root = null;
    } catch (error_) {
      console.error('Failed to unmount Excalidraw:', error_);
    }
  }
});

async function initializeExcalidraw() {
  const container = document.querySelector('#excalidraw');
  if (!container) {
    throw new Error('Excalidraw container not found');
  }

  const initialData = await getInitialData();

  const excalidrawElement = React.createElement(Excalidraw, {
    initialData,
    langCode: 'zh-CN',
    theme: 'light',
  });

  root = createRoot(container);
  root.render(excalidrawElement);

  // 添加右键菜单禁用事件监听器
  setupContextMenuDisable(container);
}

async function getInitialData() {
  try {
    const elementList = await getBizNavElementList({
      moduleCode: route.meta.moduleCode,
    });

    const elements: any[] = elementList || [];
    const appState: any = defaultAppState;

    // 如果当前 url 参数中的 design 为 1，则切换为设计模式
    const urlParams = new URLSearchParams(window.location.search);
    const isDesignMode = urlParams.get('design') === '1';
    if (isDesignMode) {
      appState.viewModeEnabled = false;
      isViewMode.value = false;
      // 启用网格模式
      appState.gridModeEnabled = true;
    }

    return {
      elements,
      appState,
    };
  } catch (error_) {
    console.error('Failed to load initial data:', error_);
    return {
      elements: [],
      appState: defaultAppState,
    };
  }
}

// 设置右键菜单禁用
function setupContextMenuDisable(container: Element) {
  const handleContextMenu = (event: Event) => {
    event.preventDefault();
    event.stopPropagation();
    return false;
  };

  // 添加右键菜单事件监听器
  container.addEventListener('contextmenu', handleContextMenu, true);

  // 清理函数（在组件卸载时调用）
  const cleanup = () => {
    container.removeEventListener('contextmenu', handleContextMenu, true);
  };

  // 将清理函数存储起来，以便在组件卸载时调用
  (container as any).__contextMenuCleanup = cleanup;
}
</script>

<style scoped lang="scss">
.excalidraw-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .excalidraw-wrapper {
    width: 100%;
    height: 100%;

    // 确保 Excalidraw 组件能够正确渲染
    :deep(.excalidraw) {
      width: 100% !important;
      height: 100% !important;
    }

    // 修复一些可能的样式冲突
    :deep(.excalidraw__canvas) {
      width: 100% !important;
      height: 100% !important;
    }
  }

  // 加载状态样式
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 16px;
    color: #666;
  }

  .error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 16px;
    color: #f56565;
  }

  // 查看模式下隐藏特定按钮
  .view-mode {
    // 隐藏左上角菜单按钮
    :deep(.excalidraw__menu-trigger),
    :deep(.excalidraw__menu-button),
    :deep([data-testid='main-menu-trigger']),
    :deep([aria-label*='Menu']),
    :deep([aria-label*='菜单']) {
      display: none !important;
    }

    // 隐藏右下角帮助按钮
    :deep(.excalidraw__help),
    :deep(.excalidraw__help-button),
    :deep([data-testid='help-button']),
    :deep([aria-label*='Help']),
    :deep([aria-label*='帮助']),
    :deep([title*='Help']),
    :deep([title*='帮助']) {
      display: none !important;
    }
  }

  // 始终隐藏素材库按钮
  :deep(.excalidraw__library),
  :deep(.excalidraw__library-button),
  :deep([data-testid='library-button']),
  :deep([data-testid='library']),
  :deep([aria-label*='Library']),
  :deep([aria-label*='素材库']),
  :deep([title*='Library']),
  :deep([title*='素材库']) {
    display: none !important;
  }
}
</style>
