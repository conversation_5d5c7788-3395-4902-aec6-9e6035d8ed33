<template>
  <div class="annex">
    <BaseUpload
      v-model:file-list="fileList"
      :editable="editable"
      @success="addContractAnnex"
      @remove="delContractAnnex"
    />
  </div>
</template>

<script lang="ts" setup>
import { inject, onBeforeMount, ref, watch } from 'vue';

import { ElMessage } from 'element-plus';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addContractAccessory,
  delContractAccessory,
  getContractAccessoryList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';

defineOptions({
  name: 'Annex',
});

const props = withDefaults(
  defineProps<{
    infoData: any;
  }>(),
  {
    infoData: {
      parentId: null,
      contractId: null,
      contractTemplateType: null,
      goodsList: [],
    },
  },
);
const editable = inject<any>('editable');
const fileList = ref([]);
// 合同的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
);

async function init() {
  getList();
}

async function delContractAnnex(data: any) {
  const id = data.id;
  const res = await delContractAccessory(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}
async function addContractAnnex(data: any) {
  const contractId = localInfoData.value.contractId;
  const params = { materialContractId: contractId, ...data };
  const res = await addContractAccessory(params);

  if (res) {
    ElMessage.success('添加成功');
  }
}
async function getList() {
  const contractId = localInfoData.value.contractId;
  const res = await getContractAccessoryList(contractId);
  // 获取地址
  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);
  const data = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
  fileList.value = data;
}

onBeforeMount(() => {
  init();
});
</script>
