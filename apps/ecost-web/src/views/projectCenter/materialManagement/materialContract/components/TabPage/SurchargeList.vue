<template>
  <div class="surchargelist h-[100%] overflow-hidden">
    <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
      <template #top></template>
      <template #seq="{ $rowIndex }">
        <div>{{ $rowIndex + 1 }}</div>
      </template>
    </VxeGrid>
  </div>
</template>
<script lang="ts" setup>
import { inject, nextTick, onBeforeMount, reactive, ref, watch } from 'vue';

import Big from 'big.js';
import { ElMessage } from 'element-plus';

import {
  editSurcharge,
  getSurchargeList,
} from '#/api/enterpriseCenter/materialManagement/materialContract';
import { amountFormat } from '#/utils/vxeTool';

defineOptions({
  name: 'SurchargeList',
});
const props = withDefaults(
  defineProps<{
    infoData: any;
  }>(),
  {
    infoData: {
      parentId: null,
      contractId: null,
      contractTemplateType: null,
      goodsList: [],
    },
  },
);
const editable: any = inject('editable');
// 合同的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
);

const tableRef = ref();

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'name',
    title: '附加费名称',
    width: '200',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '150',
  },

  {
    _type: 'patch',
    field: 'price',
    title: '变更前单价',
    width: '150',
  },
  {
    _type: 'patch',
    field: 'changePrice',
    title: '变更后单价',
    width: '150',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入变更后单价',
        type: 'float',
        digits: 6,
        autoFill: false,
      },
    },
  },
  {
    _type: 'patch',
    field: 'changeCalculatePrice',
    title: '变更计算价格',
    width: '150',
    formatter: amountFormat,
  },
  {
    _type: 'standard',
    field: 'changePrice',
    title: '增减单价',
    width: '150',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入增减单价',
        type: 'float',
        digits: 6,
        autoFill: false,
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];
let tableColumns: any = [];
tableColumns = localInfoData.value.parentId
  ? columns.filter((v) => !v._type || v._type === 'patch')
  : columns.filter((v) => !v._type || v._type === 'standard');

const tableOptions = reactive<any>({
  size: 'mini',
  height: '94%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: true,
  // rowClassName: ({ row }: any) => {
  //   return row.parentId ? '' : 'bg-gray-100';
  // },
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod() {
      if (!editable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  columns: tableColumns,
  data: [],
});

const currentItem = ref();
// 表格事件
const gridEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    const $grid = tableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
    currentItem.value = row;
  },
  async editClosed({ row }: any) {
    if (row.price && row.changePrice) {
      row.changeCalculatePrice = Big(row.changePrice)
        .minus(row.price)
        .toNumber();
    }

    const { remark, changeCalculatePrice, changePrice } = row;
    const id = row.id;
    const data = {
      changePrice,
      changeCalculatePrice,
      remark,
    };
    const res = await editSurcharge(id, data);
    if (res) {
      ElMessage.success('修改成功');
      refreshData();
    }
  },
};
async function getList() {
  tableOptions.loading = true;
  const contractId = localInfoData.value.contractId;
  const res = await getSurchargeList(contractId);
  tableOptions.data = res;
  tableOptions.loading = false;
}

// 设置高亮数据
async function setCurrentRow(
  value: string,
  key: string = 'id',
  isExpand: boolean = false,
) {
  const activeRow = tableOptions.data.find(
    (v: any) => Reflect.get(v, key) === value,
  );
  const $grid = tableRef.value;
  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentItem.value = activeRow;
    }
    if (isExpand) {
      $grid.setAllTreeExpand(true);
    }
  });
}

async function init() {
  await getList();
}

async function refreshData() {
  await getList();
  const id = currentItem.value.id;
  await setCurrentRow(id);
}

onBeforeMount(() => {
  init();
});
</script>
