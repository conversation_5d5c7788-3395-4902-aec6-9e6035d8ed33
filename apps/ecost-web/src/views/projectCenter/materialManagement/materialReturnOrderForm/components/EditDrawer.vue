<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton
                  type="primary"
                  size="default"
                  @click="insureSubmit"
                  v-auth="actionPermissions.apUpdate"
                >
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="prevBtn"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="nextBtn"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              退货单 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="退货记录" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-70px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="top-form grid grid-cols-3 gap-x-20 gap-y-1 pb-1 pt-6"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formData.orgName }}
              </ElFormItem>
              <ElFormItem
                label="采购类型："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElSelect
                  size="large"
                  v-model="formData.purchaseType"
                  placeholder=""
                  filterable
                  :disabled="!localEditable || onlyFromEntryOrder"
                >
                  <ElOption
                    v-for="v in purchaseTypeLabelOption"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="退货单位："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.supplierName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.supplierId"
                      placeholder=""
                      clearable
                      :disabled="!localEditable || !actionPermissions.apUpdate"
                      filterable
                      @change="supplierChange"
                      @clear="supplierClear"
                    >
                      <ElOption
                        v-for="v in supplierOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="合同名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.contractName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.contractId"
                      placeholder=""
                      clearable
                      :disabled="
                        !localEditable ||
                        !actionPermissions.apUpdate ||
                        formData.supplierName === '零星材料供应商'
                      "
                      filterable
                      @change="contractChange"
                      @clear="contractClear"
                    >
                      <ElOption
                        v-for="v in contractOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="退货日期："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  :clearable="false"
                  size="large"
                  v-model="formData.entryDate"
                  type="date"
                  placeholder=""
                  @change="entryDateChange"
                  :disabled="
                    !entryDateEdit ||
                    !localEditable ||
                    !actionPermissions.apUpdate
                  "
                />
              </ElFormItem>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row, $rowIndex }">
                  <div v-if="row.id">{{ $rowIndex + 1 }}</div>
                </template>
                <template #salesReturnAmount="{ row, $rowIndex }">
                  <div class="text-l text-orange-500">
                    {{ amountFormat({ cellValue: row.salesReturnAmount }) }}
                  </div>
                </template>
                <template #materialName="{ row, $rowIndex }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div v-if="row.parentId" class="pl-6">
                        <ElButton
                          size="small"
                          type="primary"
                          @click="jumpTo(row)"
                          link
                        >
                          {{ row.materialName }}
                        </ElButton>
                      </div>
                      <div v-else class="pl-3">
                        {{ row.materialName }}
                      </div>
                      <div v-if="row.isAddButtonRow">
                        <ElButton
                          size="small"
                          @click="transferDataClick({ row })"
                          :disabled="
                            !localEditable ||
                            !formData.supplierId ||
                            !dateDisAbled
                          "
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-3 gap-x-20 pt-2">
              <ElFormItem
                label="材料主管："
                label-width="88px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="退货材料员："
                label-width="98px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="收货单位接收人："
                label-width="146px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>
      </div>

      <ExtrasPanel
        :file-list="fileList"
        :visible-option="['ANNEX']"
        :editable="localEditable"
        list-type="picture"
        @del-annex="removeAnnex"
        @success-annex="addAnnex"
      />

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="在库材料"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import Big from 'big.js';
import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addAttachment,
  changeMaterialReturnSalesFormMaterialDetailSort,
  changeMaterialReturnSalesFormSubmitStatus,
  delAttachmen,
  deleteMaterialReturnSalesFormMaterialDetail,
  editMaterialReturnSalesForm,
  editMaterialReturnSalesFormMaterialDetail,
  getAttachmentList,
  getContractList,
  getMaterialReturnSalesFormMaterialDetail,
  getSupplierList,
} from '#/api/projectCenter/projectCenter/materialReturnSalesForm';
import {
  getIsEditParams,
  ParamsNamesType,
} from '#/api/systemManagementApi/orgParams';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  purchaseTypeLabelOption,
  SubmitStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import { amountFormat, setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialReturnOrderEditDrawer',
});

const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();

const { actionPermissions } = getCurrentPremission();

const router = useRouter();
// 退货日期是否可以编辑
const entryDateEdit = ref(false);
// 退货单数据只能来源于进场验收单
const onlyFromEntryOrder = ref(false);

// 附件数据
const fileList = ref([]);

// 供应商和合同名称的总数据
const contractAndSupplierOptions = ref<any>([]);

// 来自进场验收单的所有供应商
let entryCheckAllSupplierData: any[] = [];
// 来自进场验收单的所有合同
let entryCheckAllContractData: any[] = [];

// 供应商选项
const supplierOptions = ref<any>([]);
// 合同选项
const contractOptions = ref<any>([]);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);
// 表单数据
const formData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
const oldFormData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval: any) => {
    localInfoData.value = nval;
    // 调用获取材料清单列表
    await getList();
    // 调用获取附件列表
    await getAnnexlist();
    // 调用获取供应商名称
    await getSupplierContractlList();
    const { year, day, month, supplierId, creator } = nval;
    const form = {
      id: nval.id,
      orgName: nval.orgName,
      supplierId: nval.supplierId,
      contractId: nval.contractId,
      purchaseType: nval.purchaseType || '', // 默认为自采
      code: nval.code,
      supplierName: nval.supplierName,
      contractName: nval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
    formData.value = form;
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };

    // 根据供应商Id 给 合同名称Id 赋值
    if (!onlyFromEntryOrder.value) {
      if (supplierId) {
        const data = contractAndSupplierOptions.value.find(
          (item: any) => item.id === formData.value.supplierId,
        );
        contractOptions.value = data?.contracts
          ? data.contracts.map((item: any) => {
              return {
                label: item.name,
                value: item.id,
              };
            })
          : [];
      } else {
        contractOptions.value = [];
      }
    }
  },
);

const localEditable = computed(() => {
  return (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    actionPermissions.apUpdate
  );
});

watch(
  () => formData,
  (nval, oval: any) => {
    const { year, day, month } = oval;
    oldFormData.value = {
      id: oval.id,
      orgName: oval.orgName,
      supplierId: oval.supplierId,
      contractId: oval.contractId,
      purchaseType: oval.purchaseType || '', // 默认为自采
      code: oval.code,
      supplierName: oval.supplierName,
      contractName: oval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
  },
);

// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 表格配置
const emptyGoodsItem = {
  id: '',
  name: '',
  editable: false,
  disabled: false,
  isAddButtonRow: true, // 新增标识
};

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);
// 内置节点数据
// const addBtnBlockItem = {
//   id: null,
//   name: null,
//   internal: true, // 代表是内置节点
// };

const columns = [
  {
    field: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '150',
    slots: {
      default: 'materialName',
    },
    treeNode: true,
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: '150',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '150',
  },
  {
    field: 'inStockQuantity',
    title: '在库数量',
    width: '150',
  },
  {
    field: 'inStockPrice',
    title: '在库单价',
    width: '150',
  },
  {
    field: 'salesReturnQuantity',
    title: '退货数量',
    width: '150',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        enabled: false,
        placeholder: '请输入退货数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'salesReturnPrice',
    title: '退货单价',
    width: '180',
  },
  {
    field: 'salesReturnAmount',
    title: '退货金额（元）',
    width: '200',
    slots: {
      default: 'salesReturnAmount',
      footer: 'salesReturnAmount',
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '160',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  showFooter: true,
  footerData: [
    {
      id: '',
      materialName: '合计',
      internal: true,

      salesReturnAmount: 0,
    },
  ],
  cellClassName: ({ row, column }: any) => {
    const baseClass = [];
    // 保留原有的不可编辑样式逻辑
    if (
      (!row.parentId &&
        !['remark', 'salesReturnQuantity'].includes(column.field)) ||
      (row.parentId && column.field !== 'remark') ||
      !row.id
    ) {
      baseClass.push('bg-gray-100 cursor-not-allowed');
    }
    return baseClass.join(' ');
  },
  editRules: {
    salesReturnQuantity: [
      {
        required: true,
        message: '请输入小数位<=8位的数字',
        pattern: /^\d+(\.\d{1,8})?$/,
      },
    ],
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',

            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 如果是子级行（有parentId），不显示菜单
      if (row.parentId) {
        return false;
      }
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            const parentDate = tableOptions.data.filter(
              (v: any) => v.parentId === null && !v.internal,
            );
            const Index = parentDate.findIndex((v: any) => v.id === row.id);
            item.disabled = Index === parentDate.length - 1;
            break;
          }
          case 'MOVE_UP': {
            const parentDate = tableOptions.data.filter(
              (v: any) => v.parentId === null && !v.internal,
            );
            const Index = parentDate.findIndex((v: any) => v.id === row.id);
            item.disabled = Index === 0;
            break;
          }
        }
        if (!localEditable.value) {
          item.disabled = true;
        }
      });
      return !!row.id;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row, column }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前没有修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (!localEditable.value || row.internal || !row.id) {
        return false;
      }
      // 父级行：只允许编辑"退货数量"和"备注"
      if (!row.parentId && !row.internal) {
        return ['remark', 'salesReturnQuantity'].includes(column.field);
      }

      // 子级行：只允许编辑"备注"
      if (row.parentId) {
        return column.field === 'remark';
      }

      return false;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row, column }: { column: any; row: any }) {
    // 阻止内置行的点击事件
    if (row.internal) {
      return false;
    }

    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;

    // 阻止不可编辑单元格进入编辑状态
    if (
      (!row.parentId &&
        !['remark', 'salesReturnQuantity'].includes(column.field)) ||
      (row.parentId && column.field !== 'remark') ||
      !row.id
    ) {
      return false;
    }
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({ menu, row }: { menu: any; row: any; rowIndex: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await deleteMaterialReturnSalesFormMaterialDetail(id);
        if (res) {
          refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const parentDate = tableOptions.data.filter(
        (v: any) => v.parentId === null && !v.internal,
      );
      const fromId = row?.id;
      const toIndex = parentDate.findIndex((v: any) => v.id === row.id);
      const toId = parentDate[toIndex + 1]?.id;
      await changeMaterialReturnSalesFormMaterialDetailSort(fromId, toId);

      refreshData();
    }
    if (menu.code === 'MOVE_UP') {
      const parentDate = tableOptions.data.filter(
        (v: any) => v.parentId === null && !v.internal,
      );
      const fromId = row?.id;
      const toIndex = parentDate.findIndex((v: any) => v.id === row.id);
      const toId = parentDate[toIndex - 1]?.id;
      await changeMaterialReturnSalesFormMaterialDetailSort(fromId, toId);

      refreshData();
    }
  },
  // 完成编辑
  async editClosed({ row }: any) {
    const data = {
      salesReturnQuantity: row.salesReturnQuantity,
      remark: row.remark,
    };
    if (!row.salesReturnQuantity) {
      ElMessage.warning(`退货数量不能为空`);
      return;
    }
    if (row.salesReturnQuantity <= 0) {
      ElMessage.warning(`退货数量不能小于0`);
      return;
    }
    const res = await editMaterialReturnSalesFormMaterialDetail(row.id, data);
    if (res) {
      refreshData();
      ElMessage.success(`修改成功`);
    }
  },
};
// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }

  if (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    tableOptions.data.length <= 1
  ) {
    ElMessage.warning('请添加退货材料');
    return;
  }

  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const { id } = formData.value;

  const data = {
    submitStatus, // 修改提交状态为已提交
  };

  const res = await changeMaterialReturnSalesFormSubmitStatus(id, data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

const dateDisAbled = ref(true);

// 确认修改
async function insureSave() {
  if (!formData.value.entryDate) {
    ElMessage.warning('请选择退货日期');
    return;
  }
  const year = Number(dayjs(formData.value.entryDate).format('YYYY'));
  const month = Number(dayjs(formData.value.entryDate).format('M'));
  const day = Number(dayjs(formData.value.entryDate).format('D'));
  const params = {
    purchaseType: formData.value.purchaseType || null,
    supplierId: formData.value.supplierId,
    supplierName: formData.value.supplierName,
    contractId: formData.value.contractId,
    contractName: formData.value.contractName,

    year,
    month,
    day,
  };

  await editMaterialReturnSalesForm(formData.value.id, params)
    .catch((error) => {
      if (error.code === 400) {
        dateDisAbled.value = false;
      }
    })
    .then((res) => {
      if (res) {
        dateDisAbled.value = true;
      }
    });
}

// 供应商改变
async function supplierChange() {
  // 清单中有数据则不可修改
  // 过滤内置数据
  const data = tableOptions.data.filter(
    (v: any) => !v.internal && !v.isAddButtonRow,
  );
  if (data.length > 0) {
    // 还原为旧值
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;

      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;

      formData.value.purchaseType = oldFormData.value.purchaseType;
    });

    ElMessage.warning('请先删除材料数据后再修改');

    return;
  }

  // 根据 数据是否只能来源于进场验收单 区分处理
  if (onlyFromEntryOrder.value) {
    // 筛选出合同的数据
    contractOptions.value = entryCheckAllContractData.filter(
      (v: any) => formData.value.supplierId === v.supplierId,
    );
    // 同时给名称赋值
    const data = supplierOptions.value.find(
      (item: any) => item.value === formData.value.supplierId,
    );
    formData.value.supplierName = data.label;
    // 给采购类型赋值
    formData.value.purchaseType = data.purchaseType;
  } else {
    // 同时给名称赋值
    const data = contractAndSupplierOptions.value.find(
      (item: any) => item.id === formData.value.supplierId,
    );
    formData.value.supplierName = data.name;

    // 筛选出合同的数据
    contractOptions.value = data?.contracts
      ? data.contracts.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        })
      : [];
  }

  formData.value.contractId = '';
  formData.value.contractName = '';
  if (contractOptions.value.length === 1) {
    formData.value.contractId = contractOptions.value[0].value;
    formData.value.contractName = contractOptions.value[0].label;
  }
  await insureSave();
}

// 合同改变
async function contractChange() {
  // 清单中有树据则不可修改
  // 过滤内置数据
  const data = tableOptions.data.filter(
    (v: any) => !v.internal && !v.isAddButtonRow,
  );
  if (data.length > 0) {
    // 还原为旧值
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;

      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;

      formData.value.purchaseType = oldFormData.value.purchaseType;
    });

    ElMessage.warning('请先删除材料数据后再修改');
    return;
  }

  if (onlyFromEntryOrder.value) {
    // 合同改变 先找出合同的数据  再通过合同的supplierId 筛选出符合的所有供应商
    const contract = entryCheckAllContractData.find(
      (v: any) => formData.value.contractId === v.value,
    );

    supplierOptions.value = entryCheckAllSupplierData.filter(
      (v: any) => contract.supplierId === v.value,
    );

    formData.value.supplierId = '';
    formData.value.supplierName = '';
    if (supplierOptions.value.length === 1) {
      formData.value.supplierId = supplierOptions.value[0].value;
      formData.value.supplierName = supplierOptions.value[0].label;
    }
    // 同时给名称赋值
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );
    formData.value.contractName = data.label;
    // 给采购类型赋值
    formData.value.purchaseType = data.purchaseType;
  } else {
    // 同时给名称赋值
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );
    formData.value.contractName = data.label;
  }
  await insureSave();
}

// 收料日期改变
async function entryDateChange() {
  await insureSave();
}

async function supplierClear() {
  resetData();
}

async function contractClear() {
  resetData();
}

async function resetData() {
  supplierOptions.value = entryCheckAllSupplierData;
  contractOptions.value = entryCheckAllContractData;

  formData.value.supplierId = '';
  formData.value.supplierName = '';
  formData.value.contractId = '';
  formData.value.contractName = '';

  formData.value.purchaseType = '';
}

// 弹窗打开回调
async function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);
// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getMaterialReturnSalesFormMaterialDetail(id);

  tableOptions.data =
    res.length === 0 ? [emptyGoodsItem] : [...res, emptyGoodsItem];
  // tableOptions.data.unshift(countBlockItem);
  const totalItem = tableOptions.footerData[0];
  // 重置合计金额
  totalItem.salesReturnAmount = 0;

  // 计算合计金额
  tableOptions.data.forEach((item: any) => {
    if (!item.parentId) {
      // 确保处理的是有效的数值
      const amount = Number.parseFloat(item.salesReturnAmount) || 0;
      totalItem.salesReturnAmount = Big(totalItem.salesReturnAmount)
        .plus(amount)
        .round(2, Big.roundHalfUp)
        .toNumber();
    }
  });
}

const receivingMinDate = ref('');

// 点击穿梭框
async function transferDataClick() {
  addMaterialinfoData.value = {
    returnSalesFormId: formData.value.id,
  };

  addOrEditMaterialVisible.value = true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}

// 获取供应商名称
async function getSupplierContractlList() {
  // 如果数据强制来源于进场验收单则调用与进场验收单审批过的数据 否则 则是所有数据
  await getSupplierOption();
  await getContractlOption();
}

// 获取供应商
async function getSupplierOption() {
  const supplierRes = await getSupplierList();
  entryCheckAllSupplierData = supplierRes.map((item: any) => {
    return {
      contractId: item.contractId,
      purchaseType: item.purchaseType,

      label: item.supplierName,
      value: item.supplierId,
    };
  });

  supplierOptions.value = entryCheckAllSupplierData;
}

// 获取合同
async function getContractlOption() {
  const ContractRes = await getContractList();
  entryCheckAllContractData = ContractRes.map((item: any) => {
    return {
      supplierId: item.supplierId,
      purchaseType: item.purchaseType,

      label: item.contractName,
      value: item.contractId,
    };
  });

  contractOptions.value = entryCheckAllContractData;
}

// 新增附件
async function addAnnex(data: any) {
  const _data = {
    returnSalesFormId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addAttachment(_data);
  if (res) {
    ElMessage.success('添加成功');
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;
  const res = await delAttachmen(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}
// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}

async function init() {
  const paramsNames = `${ParamsNamesType.BASE_ACCOUNT},${ParamsNamesType.MATERIAL_RECEIVING}`;
  const res = await getIsEditParams(paramsNames);
  const isEntryDateEdit = res[ParamsNamesType.BASE_ACCOUNT];
  const isOnlyFromEntryOrder = res[ParamsNamesType.MATERIAL_RECEIVING];
  onlyFromEntryOrder.value = isOnlyFromEntryOrder.isMaterialIncomingInspection;
  entryDateEdit.value = isEntryDateEdit.modifyAccountTime;
}

const jumpTo = (row: any) => {
  router.push({
    name: 'MenuMaterialReceivingForm',
    query: { code: row.detailCode },
  });
};

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

onBeforeMount(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 16px;
}
</style>
