<template>
  <div class="selections h-full">
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <template #type="{ row }">
              <div>
                {{ getMaterialTypeLabel(row.type) }}
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, watch } from 'vue';

import {
  getMaterialTypeLabel,
  materialTypeLabelOption,
} from '#/types/materialManagement';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionClassData: any[]; // 选择的类别数据
    selectionData: any[]; // 选择的明细数据
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionClassData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'selectAll', data: any): void;

  (e: 'add', data: any): void; // 添加到已选明细的 数据
}>();

const multiple = inject<any>('multiple'); // 是否是多选
// 当前选中的分类表格数据
const currentClassItem = ref();
// 分类表格数据
const prevTableRef = ref();
// 内置分类数据
const staticClassItem = {
  id: '',
  name: '全部',
  parentId: null,
  remark: '',
  type: '',
  disabled: false,
};
// 分类表格配置
const prevColumns = [
  {
    field: 'code',
    title: '编码',
    width: '80',
    treeNode: true,
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编码',
      },
    },
  },
  {
    field: 'name',
    title: '类别名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入类别名称',
      },
    },
  },
  {
    field: 'type',
    title: '核算类型',
    width: '100',
    slots: {
      default: 'type',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: materialTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择核算类型',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-100';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns: prevColumns,
  data: props.choiceClassData,
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = [staticClassItem, ...nval];
    if (prevTableOptions.data.length > 0) {
      const currentRow = prevTableOptions.data[0];

      setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow);
      emit('select', currentRow);
    }
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    tableOptions.data = [];
    if (row.disabled) {
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }

    currentClassItem.value = row;
    emit('select', row);
  },
  // cellDblclick({ row }: any) {
  //   tableOptions.data = [];
  //   if (row.disabled || row.selected) {
  //     const $grid = prevTableRef.value;
  //     if ($grid) {
  //       $grid.clearCurrentRow();
  //     }
  //     return; // 如果是禁用状态则不可选
  //   }
  //   currentClassItem.value = row;
  //   // row.selected = true;

  //   emit('selectAll', row);
  // },
};

// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    file: 'seq',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '在库材料名称',
    width: '120',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入在库材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入计量单位',
      },
    },
  },
  {
    field: 'inventoryQuantity',
    title: '在库数量',
    width: '80',
  },
  {
    field: 'price',
    title: '单价',
    width: '80',
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled
      ? 'bg-gray-100 cursor-not-allowed opacity-70'
      : row.selected
        ? 'bg-orange-100'
        : '';
  },
  columns,
  data: props.choiceDetailData,
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

// 表格数据状态修改
const selections = ref(props.selectionData); // 已选数据
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = multiple.value ? false : !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    // 如果行被禁用，阻止事件传播
    if (row.disabled || row.selected) {
      return false;
    }
    emit('add', row);
  },
};
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
