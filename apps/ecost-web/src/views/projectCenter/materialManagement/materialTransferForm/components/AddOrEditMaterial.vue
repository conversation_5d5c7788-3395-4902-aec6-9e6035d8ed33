<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="false"
    :title="title"
    @close="dialogClose"
    @open="dialogOpen"
    top="2%"
    :style="{ width: '80%' }"
  >
    <TransferSelector
      ref="transferSelectorRef"
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      :cur-tab="curTab"
      display="col"
      @select="classSelect"
    />

    <template #footer>
      <ElButton @click="dialogClose">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, ref, watch } from 'vue';

import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  addInspectionDetail,
  getMaterialCategory,
  getMaterialDetail,
} from '#/api/enterpriseCenter/materialTransferForm/materialTransferForm';

import TransferSelector from './TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}
const props = withDefaults(
  defineProps<{
    infoData: {
      materialSearchType?: string;
      purchaseType: string;
      receivingId: string;
    };
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    infoData: () => {
      return {
        receivingId: '',
        materialSearchType: '',
        purchaseType: '',
      };
    },
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();
const transferSelectorRef = ref();
// 当前选择的数据
const curTab = inject<any>('curTab');

// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
  { deep: true, immediate: true },
);
// 弹窗是否展示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);
// 获取分类列表
async function getCategoryList() {
  const res = await getMaterialCategory();
  choiceClassData.value = res.map((item: any) => {
    return {
      ...item,
      selected: false,
    };
  });
}
// 分类选择后获取明细列表
async function classSelect(row: any) {
  const categoryId = row.id;
  const materialAllocationFromId = localInfoData.value.receivingId;
  const res = await getMaterialDetail(categoryId, materialAllocationFromId);

  const ids = new Set(selectionData.value.map((v: any) => v.id));
  const data = res.map((item: any) => {
    const selected = !!ids.has(item.id);
    return {
      ...item,
      id: item.id, // 给id重新赋值为材料id
      selected,
      disabled: item.disabled,
    };
  });
  choiceDetailData.value = res ? data : [];
}

// 关闭弹窗
function dialogClose() {
  selectionData.value = [];
  emit('update:visible', false);
}

// 开启弹窗   // 每次传值的时候调用分类列表
function dialogOpen() {
  getCategoryList();
}

// 提交
const submit = async () => {
  const errMsg = await transferSelectorRef.value.validateSelections();
  if (errMsg) return;

  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );

  const receivingId = localInfoData.value.receivingId;

  const data = filterSelectionData.map((item: any) => {
    return {
      materialId: item.materialId, // 材料id
      materialName: item.materialName, // 材料名称
      materialSpec: item.materialSpec, // 规格
      unit: item.unit, // 单位
      inStockQuantity: item.inventoryQuantity, // 可调拨数量
      inStockPrice: item.price, // 单价
      allocationQuantity: item.allocationQuantity, // 调拨数量
    };
  });

  if (data.length <= 0) {
    ElMessage.warning('请先添加材料');
    return;
  }

  const res = await addInspectionDetail(receivingId, data);

  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
</script>
<style scoped lang="scss"></style>
