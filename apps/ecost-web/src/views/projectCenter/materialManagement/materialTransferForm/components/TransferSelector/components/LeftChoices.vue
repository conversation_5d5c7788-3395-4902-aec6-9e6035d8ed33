<template>
  <div class="selections h-full">
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="item-center mb-[8px] flex h-[30px] gap-6">
          <TreeLevelExpand
            :expand-idx="expandIdx"
            @expand-click="expandClick"
          />
        </div>
        <div class="h-[calc(100%-38px)]">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #type="{ row }">
              <div>{{ getMaterialTypeLabel(row.type) }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="item-center mb-[8px] flex h-[30px]">
          <div class="leading-[30px]">查看明细</div>
        </div>

        <div class="h-[calc(100%-38px)]">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #type="{ row }">
              <div>{{ getMaterialTypeLabel(row.type) }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';

import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import {
  getMaterialTypeLabel,
  materialTypeLabelOption,
} from '#/types/materialManagement';
import {
  flattenTreeToLevel,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[];
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'add', data: any): void;
}>();

// 展开层级下标
const expandIdx = ref(0);
// 当前选中的分类表格数据
const currentClassItem = ref({
  id: '',
});
// 分类表格数据
const prevTableRef = ref();
// 内置分类数据
const staticClassItem = {
  id: '',
  name: '全部',
  parentId: null,
  remark: '',
  type: '',
  disabled: true,
};
// 分类表格配置
const prevColumns = [
  {
    field: 'code',
    title: '编码',
    width: '80',
  },
  {
    field: 'name',
    title: '类别名称',
    minWidth: '100',
    treeNode: true,
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请选择结算状态',
      },
    },
  },
  {
    field: 'type',
    title: '核算类型',
    minWidth: '100',
    slots: {
      default: 'type',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect',
      options: materialTypeLabelOption,
      props: {
        size: 'mini',
        placeholder: '请选择核算类型',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: '80',
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columns: prevColumns,
  data: [],
});

// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    if (row.id === currentClassItem.value.id) return;
    currentClassItem.value = row;
    emit('select', row);
  },
};
// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: ' ',
    width: '60',
  },
  {
    field: 'materialName',
    title: '在库材料名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入在库材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入计量单位',
      },
    },
  },
  {
    field: 'inventoryQuantity',
    title: '库存数量',
    width: '80',
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-100';
    }
    if (row.selected) {
      return 'bg-orange-100';
    }

    return '';
  },
  treeConfig: {
    transform: false,
  },
  columns,
  data: [],
});
// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (!row.selected && !row.disabled) {
      emit('add', row);
    }
  },
};

// 已选数据
const selections = ref(props.selectionData);

// 分类数据变化
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = [staticClassItem, ...nval];
    init();
  },
  { immediate: true },
);

// 明细数据 变化
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;

    const ids = new Set(selections.value.map((item) => item.id));

    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 已选数据 变化
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));

    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 表格展开
const expandClick = (level: number) => {
  expandIdx.value = level;
  const $grid = prevTableRef.value;
  $grid.clearTreeExpand();
  if (level === 0) {
    $grid.setAllTreeExpand(true);
  } else {
    const tableData = $grid.getTableData();
    const targetData = flattenTreeToLevel(tableData.fullData, level);
    $grid.setTreeExpand(targetData, true);
  }
};

// 初始化
function init() {
  // 默认选中第一项
  if (prevTableOptions.data.length > 0) {
    const currentRow = prevTableOptions.data[0];
    setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow, true);
    emit('select', currentRow);
  }
}

onMounted(() => {});
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
