<template>
  <div class="selections h-full w-full">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>确认材料</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: ' ',
    width: '60',
  },
  {
    field: 'materialName',
    title: '在库材料名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入在库材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '150',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入计量单位',
      },
    },
  },
  {
    field: 'inventoryQuantity',
    title: '库存数量',
    minWidth: '80',
  },
  {
    field: 'allocationQuantity',
    title: '调拨数量',
    minWidth: '80',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入库存数量',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    // width: '80',
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  editRules: {
    allocationQuantity: [
      {
        required: true,
        // message: '请输入小数位<=8位的调拨数量',
        // pattern: /^(?!0+(?:\.0+)?$)\d+(?:\.\d{1,8})?$/,
        validator: ({ row }: any) => {
          if (row.allocationQuantity <= 0) {
            return new Error('调拨数量必须大于0');
          }
          const pattern = /^\d+(?:\.\d{1,8})?$/;
          if (!pattern.test(row.allocationQuantity)) {
            return new Error('请输入小数位<=8位的调拨数量');
          }

          if (row.allocationQuantity > row.inventoryQuantity) {
            return new Error('调拨数量不能大于库存数量');
          }
        },
      },
    ],
  },
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  cellClassName: ({ column }: any) => {
    return column.editRender ? '' : 'bg-gray-100';
  },
  columns,
  data: props.selectionData,
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex, column }: any) {
    if (column.editRender) return;
    if (row.disabled) return;

    emit('remove', { row, rowIndex });
  },
  async editClosed({ row }: any) {
    const errMsg = await tableRef.value.validate(row);
    if (errMsg) return;
    const $gird = tableRef.value;
    if ($gird && $gird.isUpdateByRow(row)) {
      $gird.reloadRow(row, {});
    }
  },
};

async function validateTable() {
  return await tableRef.value.validate(true);
}

defineExpose({
  validateTable,
});
</script>
