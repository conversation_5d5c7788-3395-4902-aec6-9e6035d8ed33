<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <div class="area-left h-full pr-2">
        <TimeSelect
          ref="timeSelectEl"
          :time-data="timeSelectData"
          @select="timeSelect"
        />
      </div>
      <div class="area-right h-full flex-1 overflow-auto">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top>
            <div class="flex h-[48px] items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton type="primary" size="small" @click="debounceAddData">
                  新增
                </ElButton>
                <div class="ml-3 mr-3">
                  <ElDropdown
                    split-button
                    size="small"
                    type="primary"
                    @command="handleDropDownItem"
                    @click="handleImportExcel"
                    :disabled="
                      !actionPermissions.apImport || !needHistoryAccount
                    "
                  >
                    批量导入
                    <template #dropdown>
                      <ElDropdownMenu>
                        <ElDropdownItem command="download">
                          模版下载
                        </ElDropdownItem>
                      </ElDropdownMenu>
                    </template>
                  </ElDropdown>
                  <input
                    ref="fileInput"
                    type="file"
                    style="display: none"
                    @change="handleFileChange"
                  />
                </div>
                <ElButton type="default" size="small" @click="filter">
                  重置筛选
                </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <!-- <ElButton type="default" size="small"> 导出单据 </ElButton> -->
                </div>
                <div class="mb-2">
                  <!-- <ElButton type="default" size="small"> 导出列表 </ElButton> -->
                </div>
              </div>
            </div>
          </template>

          <!-- <template #seq="{ rowIndex }">
            <div>{{ rowIndex + 1 }}</div>
          </template> -->
          <template #code="{ row }">
            <div>
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
          </template>

          <template #purchaseType="{ row }">
            <div>
              {{ getPurchaseTypeLabel(row.purchaseType) }}
            </div>
          </template>

          <template #materialReceiptStatus="{ row }">
            <div
              :class="{
                'text-green-500':
                  row.materialReceiptStatus === MaterialReceiptStatus.RECEIVED,
                'text-red-500':
                  row.materialReceiptStatus ===
                  MaterialReceiptStatus.UN_RECEIVED,
              }"
            >
              {{ getMaterialReceiptStatusLabel(row.materialReceiptStatus) }}
            </div>
          </template>

          <template #createAt="{ row }">
            <div>
              {{
                dayjs(`${row.year}/${row.month}/${row.day}`).format(
                  'YYYY-MM-DD',
                )
              }}
            </div>
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.qrCodeUrl" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.qrCodeUrl" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </VxeGrid>
      </div>
    </div>
    <ImportExcelData
      v-model="importExcelDataVisible"
      :records="importExcelErrorData"
      :total-count="importExcelTotalCount"
      :error-count="importExcelErrorCount"
      :success-count="importExcelSuccessCount"
      @refresh="refreshData"
    />
    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractSelectMove"
      @refresh="refreshData"
    />
  </Page>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElMessage,
  ElMessageBox,
  ElPopover,
} from 'element-plus';
import _ from 'lodash';
import QrcodeVue from 'qrcode.vue';

import { fileCloudUpload } from '#/api/couldApi';
import {
  addInspectionBill,
  delInspectionBill,
  editInspectionBill,
  getInspectionBillList,
  getTimeList,
  importExcelData,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import {
  getIsEditParams,
  ParamsNamesType,
} from '#/api/systemManagementApi/orgParams';
import TimeSelect from '#/components/TimeSelect/TimeSelect.vue';
import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getMaterialReceiptStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  MaterialReceiptStatus,
  materialReceiptStatusOption,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { downloadLocalFile } from '#/utils';
import { getCurrentPremission } from '#/utils/permission';
import {
  findNextOrPrevRow,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';
import ImportExcelData from './components/ImportExcelData.vue';

defineOptions({
  name: 'MaterialEntryCheck',
});

const timeSelectData = ref([]);

// 新增弹窗是否展示
const addFromDialogVisible = ref(false);

const route = useRoute();

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

const timeSelectEl = ref();
const tableRef = ref();

const addOrEditContractForm = ref<any>({
  // 新增/编辑表单
  id: null,
  parentId: null,

  name: '',
  code: '',
  partyA: '',
  partyB: '',
  contractTemplateId: '',
  partyBType: '',
  proposedStatus: '',
});

// 当前选择的时间
const currentTimeItem = ref();

// 当前点击项
const currentItem = ref<any>();

// 表格配置
const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: ' ',
    width: '60',
    fixed: 'left',
    // slots: {
    //   default: 'seq',
    // },
  },
  {
    field: 'materialReceiptStatus',
    title: '收料状态',
    width: '80',
    fixed: 'left',
    slots: {
      default: 'materialReceiptStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: materialReceiptStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择收料状态',
      },
    },
  },
  {
    field: 'code',
    title: '单据编码',
    width: '120',
    fixed: 'left',
    slots: {
      default: 'code',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '80',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: '180',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: '180',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '180',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'creator',
    title: '编制人',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人名称',
      },
    },
  },
  {
    field: 'createAt',
    title: '进场日期',
    width: '100',
    slots: {
      default: 'createAt',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '100',
    editRender: {
      name: 'VxeSelect',
      options: auditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
  {
    title: '二维码',
    width: '80',
    slots: {
      default: 'qrcode',
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;

    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionBill(id);
        if (res) {
          await refreshData();
          await getTimeSelectData();
          ElMessage.success('删除成功');
        }
      });
    }

    if (menu.code === 'EDIT_ROW') {
      const {
        id,
        parentId,
        name,
        code,
        partyA,
        partyB,
        contractTemplateId,
        partyBType,
        proposedStatus,
      } = row;
      addOrEditContractForm.value = row.parentId
        ? {
            id,
            parentId,
            name,
            code,
            contractTemplateId,
          }
        : {
            id,
            parentId,
            name,
            code,
            partyA,
            partyB,
            contractTemplateId,
            partyBType,
            proposedStatus,
          };

      addFromDialogVisible.value = true;
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = auditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );
      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const data = {
            id,
            auditStatus: row.auditStatus, // 修改提交状态为已提交
          };

          const res = await editInspectionBill(data);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};

// 新增数据
async function addData() {
  const res = await addInspectionBill();
  const itemData = {
    id: res.id,
    orgName: res.orgName,
    auditStatus: res.auditStatus,
    code: res.code,
    creator: res.creator,
    day: res.day,
    month: res.month,
    year: res.year,
    purchaseType: res.purchaseType,
    submitStatus: res.submitStatus,
    materialReceiptStatus: res.materialReceiptStatus,

    supplierId: null,
    supplierName: null,
    contractId: null,
    contractName: null,
  };
  infoData.value = itemData;
  currentItem.value = itemData;
  await refreshData();
  await getTimeSelectData();
  drawerVisible.value = true;
}

async function filter() {
  const $grid = tableRef.value;
  resetFilter(tableOptions.columns, $grid);
}

const debounceAddData = _.debounce(addData, 500);

// 打开详情
async function openDetail(row: any, isOpen = true) {
  infoData.value = {
    id: row.id,
    orgName: row.orgName,
    auditStatus: row.auditStatus,
    code: row.code,
    creator: row.creator,
    day: row.day,
    month: row.month,
    year: row.year,

    purchaseType: row.purchaseType,
    submitStatus: row.submitStatus,
    materialReceiptStatus: row.materialReceiptStatus,

    supplierId: row.supplierId,
    supplierName: row.supplierName,
    contractId: row.contractId,
    contractName: row.contractName,
  };
  if (isOpen) {
    drawerVisible.value = true;
  }
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const targetItem = findNextOrPrevRow($grid, move);
  if (targetItem) {
    tableEvents.cellClick({ row: targetItem });
    setCurrentRow(tableOptions.data, tableRef.value, targetItem);
    openDetail(targetItem, false);
  }
}

// 刷新
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

// 时间选择
async function timeSelect(row: any) {
  currentTimeItem.value = row;

  await getList();
}

// 获取时间选择框数据 并展开
async function getTimeSelectData() {
  const res = await getTimeList();

  timeSelectData.value = res;
}

// 初始化
async function init() {
  await getTimeSelectData();

  await getList();

  await getOrgParams();
}
// 获取物资进场验收数据
async function getList() {
  tableOptions.loading = true;

  const params: any = {};
  if (currentTimeItem.value?.year) {
    params.year = currentTimeItem.value.year;
  }
  if (currentTimeItem.value?.month) {
    params.month = currentTimeItem.value.month;
  }
  if (currentTimeItem.value?.day) {
    params.day = currentTimeItem.value.day;
  }
  const res = await getInspectionBillList(params);

  tableOptions.data = res;
  tableOptions.loading = false;

  openRoutePage();
}

const { actionPermissions } = getCurrentPremission();

// 数据补录参数设置
const needHistoryAccount = ref(true);
async function getOrgParams() {
  const orgParams = await getIsEditParams(
    ParamsNamesType.MATERIAL_INCOMING_INSPECTION,
  );
  needHistoryAccount.value =
    orgParams[ParamsNamesType.MATERIAL_INCOMING_INSPECTION].needHistoryAccount;
}

// 打开跳转的存储页面
function openRoutePage() {
  if (route.query.code && tableRef.value) {
    const target = tableOptions.data.find(
      (v: any) => v.code === route.query.code,
    );
    setTimeout(() => {
      tableRef.value.scrollToRow(target);
    }, 1000);
    currentItem.value = target;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
    openDetail(target);
  }
}

// #region 文件导入

function handleDropDownItem(command: string) {
  if (command === 'download') {
    downloadLocalFile('/file/物资进场验收记录.xlsx', '物资进场验收记录.xlsx');
  }
}

const fileInput = ref<HTMLInputElement | null>(null);
async function handleImportExcel() {
  if (fileInput.value) {
    fileInput.value?.click();
  }
}

const importExcelDataVisible = ref(false);
const importExcelErrorData = ref<any[]>([]);
const importExcelTotalCount = ref(0);
const importExcelErrorCount = ref(0);
const importExcelSuccessCount = ref(0);
async function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    // 1. 上传文件
    const fileUploadRes = await fileCloudUpload(file, {
      isUpload: true,
      isTempFile: true,
    });

    // 2.调用导入接口
    const importData = await importExcelData({
      type: 'materialIncomingInspectionImport',
      fileKey: fileUploadRes.fileKey,
    });

    if (!_.isEmpty(importData.errorData)) {
      importExcelDataVisible.value = true;
      importExcelErrorData.value = importData.errorData;
      importExcelTotalCount.value = importData.totalCount;
      importExcelErrorCount.value = importData.errorCount;
      importExcelSuccessCount.value = importData.successCount;
    }
  }
  target.value = '';
}

// #endregion

// 渲染前
onBeforeMount(async () => {
  init();
});
</script>

<style lang="scss" scoped>
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
