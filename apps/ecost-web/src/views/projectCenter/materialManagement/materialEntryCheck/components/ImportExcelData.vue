<template>
  <ElDialog
    v-model="visible"
    title="导入问题数据"
    width="80%"
    @opened="handleOpenedDialog"
    :before-close="handleCloseDialog"
    :z-index="998"
  >
    <div>
      <VxeGrid
        v-bind="tableOptions"
        @current-row-change="handleRowChange"
        ref="gridRef"
      />
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <span style="mr-10">
            导入成功行数={{ props.successCount }}，导入失败行数计数={{
              props.errorCount
            }}
            ，合计={{ props.totalCount }}
          </span>
        </div>
        <div>
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleConfirm"> 确定 </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<script lang="ts" setup>
import type { PurchaseTypeEnum } from '#/types/materialManagement';

import { computed, defineEmits, defineProps, reactive, ref } from 'vue';

import { ElButton, ElDialog, ElMessageBox } from 'element-plus';
import _ from 'lodash';

import {
  bulkSaveData,
  getMaterialDetailList,
  getSupplierAndContractList,
  getUnitSelectionData,
  MaterialSearchType,
} from '#/api/enterpriseCenter/materialEntryCheck/materialEntryCheck';
import {
  getPurchaseTypeLabel,
  PurchaseType,
  purchaseTypeLabelOption,
} from '#/types/materialManagement';
import { vxeBaseConfig } from '#/utils/vxeTool';

const props = defineProps({
  modelValue: Boolean,
  records: {
    type: Array,
    default: () => [],
  },
  totalCount: {
    type: Number,
    default: 0,
  },
  errorCount: {
    type: Number,
    default: 0,
  },
  successCount: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits<{
  refresh: any;
  'update:modelValue': [value: boolean];
}>();

const gridRef = ref();

// 使用计算属性来处理 v-model
const supplierNameMap: Record<string, string> = {};
const contractNameMap: Record<string, string> = {};
// 弹窗打开回调
function handleOpenedDialog() {
  tableOptions.data = props.records;

  // 记录供应商和合同id=>name的映射
  for (const item of props.records as any) {
    if (item.supplierId) {
      supplierNameMap[item.supplierId] = item.supplierName;
    }
    if (item.contractId) {
      contractNameMap[item.contractId] = item.contractName;
    }
  }
}

function getSupplierName(supplierId: string, supplierName?: any) {
  if (supplierName) {
    return supplierName;
  }
  return supplierNameMap[supplierId] || supplierId;
}

function getContractName(contractId: string, contractName?: any) {
  if (contractName) {
    return contractName;
  }
  return contractNameMap[contractId] || contractId;
}

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 处理取消按钮
const handleCancel = () => {
  ElMessageBox.confirm('弹窗关闭后，数据不会保存！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    visible.value = false;
    emit('refresh');
  });
};
// 弹窗关闭回调
const handleCloseDialog = (done: () => void) => {
  ElMessageBox.confirm('弹窗关闭后，数据不会保存！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    visible.value = false;
    done();
    emit('refresh');
  });
};

// 处理确定按钮
async function handleConfirm() {
  const $grid = gridRef.value;
  if ($grid) {
    const errMap = await $grid.validate(true);
    if (!errMap) {
      const res = await bulkSaveData(tableOptions.data);
      if (res) {
        visible.value = false;
        emit('refresh');
      }
    }
  }
}

// 供应商名录下拉数据
const supplierOptions = ref<{ label: string; value: any }[]>([]);
// 合同下拉数据
const contractOptions = ref<{ label: string; value: any }[]>([]);
// 材料下拉数据
const materialOptions = ref<{ label: string; value: any }[]>([]);
// 计量单位下拉数据
const unitOptions = ref<{ label: string; value: any }[]>([]);

async function handleRowChange({ row }: { row: any }) {
  if (row.purchaseType?.validMsg) return;

  const purchaseType = row.purchaseType;
  const supplierAndContracts: any = await getSupplierAndContracts(purchaseType);
  refreshSupplierAndContractSelectionData(row, supplierAndContracts);

  const materials: any = await getMaterialDetails(row);
  refreshMaterialSelectionData(materials);
}

function refreshSupplierAndContractSelectionData(
  row: any,
  supplierAndContracts: any,
) {
  supplierOptions.value = [];
  contractOptions.value = [];
  // 记录id=>name映射
  for (const supplier of supplierAndContracts) {
    supplierNameMap[supplier.id] = supplier.name;
    supplierOptions.value.push({
      label: supplier.name,
      value: supplier.id,
    });
    if (supplier.contracts) {
      for (const contract of supplier.contracts) {
        contractNameMap[contract.id] = contract.name;
        contractOptions.value.push({
          label: contract.name,
          value: contract.id,
        });
      }
    }
  }

  // 如果供应商已经有值，合同只能选择供应商下的合同
  if (
    (row.purchaseType === PurchaseType.SELF_PURCHASE ||
      row.purchaseType === PurchaseType.CENTRALIZED_PURCHASE ||
      row.purchaseType === PurchaseType.PARTY_A_DIRECTED) &&
    row.supplierId
  ) {
    const contracts =
      supplierAndContracts.find((item: any) => item.id === row.supplierId)
        ?.contracts || [];
    for (const contract of contracts) {
      contractOptions.value.push({
        label: contract.name,
        value: contract.id,
      });
    }
  }
}

// 获取对应采购类型的下拉数据
const purchaseTypeSelectionDataMap: Partial<Record<PurchaseTypeEnum, any>> = {};
async function getSupplierAndContracts(purchaseType: PurchaseTypeEnum) {
  if (!purchaseTypeSelectionDataMap[purchaseType]) {
    // 获取供应商和合同数据
    purchaseTypeSelectionDataMap[purchaseType] =
      await getSupplierAndContractList({
        purchaseType,
      });
  }

  return purchaseTypeSelectionDataMap[purchaseType];
}

// 刷新材料下拉数据
function refreshMaterialSelectionData(materials: any) {
  materialOptions.value = [];
  for (const m of materials) {
    materialOptions.value.push({
      label: `${m.code}-${m.name}-${m.spec}`,
      value: m.code,
    });
  }
}

// 获取材料明细
const materialSelectionDataMap: Record<string, any[]> = {};
async function getMaterialDetails(row: any) {
  const { contractId } = row;
  if (!contractId) return [];

  const materialSearchType = contractId
    ? MaterialSearchType.CONTRACT
    : MaterialSearchType.MATERIAL_DICT;
  if (!materialSelectionDataMap[contractId]) {
    // 获取材料明细数据
    materialSelectionDataMap[contractId] = await getMaterialDetailList({
      contractId,
      materialSearchType,
      materialCategoryId: '',
    });
  }

  return materialSelectionDataMap[contractId];
}

function refreshMaterialUnitSelectionData(units: string[]) {
  unitOptions.value = [];
  for (const item of units) {
    unitOptions.value.push({
      label: item,
      value: item,
    });
  }
}

// 获取材料单位
const materialUnitSelectionDataMap: Record<string, string[]> = {};
async function getMaterialUnitSelectionData(
  contractId: string,
  materialId: string,
) {
  if (contractId) {
    if (!materialUnitSelectionDataMap[`${contractId}@${materialId}`]) {
      const res = await getUnitSelectionData(contractId, [materialId]);
      materialUnitSelectionDataMap[`${contractId}@${materialId}`] = res.find(
        (i: any) => i.materialId === materialId,
      )?.units;
    }
    return materialUnitSelectionDataMap[`${contractId}@${materialId}`];
  } else {
    if (!materialUnitSelectionDataMap[materialId]) {
      const res = await getUnitSelectionData(null, [materialId]);
      materialUnitSelectionDataMap[materialId] = res.find(
        (i: any) => i.materialId === materialId,
      )?.units;
    }
    return materialUnitSelectionDataMap[materialId];
  }
}

// 表格配置
const columns = [
  {
    field: 'rowNo',
    title: '行数',
    width: 50,
  },
  {
    field: 'siteEntryDate',
    title: '进场日期',
    width: 100,
    editRender: { name: 'VxeDatePicker', props: { type: 'date' } },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return getPurchaseTypeLabel(cellValue);
    },
    editRender: {
      name: 'VxeSelect',
      options: purchaseTypeLabelOption,
      props: {
        filterable: true,
      },
      events: {
        change: async ({ row }: any) => {
          row.purchaseTypeMsg = '';
          row.supplierId = '';
          row.contractId = '';
          row.supplierName = '';
          row.supplierNameMsg = '';
          row.contractName = '';
          row.contractNameMsg = '';
          row.materialId = '';
          row.materialCode = '';
          row.materialCodeMsg = '';
          row.materialName = '';
          row.materialSpec = '';
          row.unit = '';
          row.unitMsg = '';

          const supplierAndContracts = await getSupplierAndContracts(
            row.purchaseType,
          );
          refreshSupplierAndContractSelectionData(row, supplierAndContracts);
        },
      },
    },
  },
  {
    field: 'supplierId',
    title: '供应商名称',
    width: 150,
    formatter({ cellValue, row }: { cellValue: any; row: any }) {
      return getSupplierName(cellValue, row.supplierName);
    },
    editRender: {
      name: 'VxeSelect',
      options: supplierOptions,
      props: {
        filterable: true,
      },
      events: {
        change: async ({ row }: any) => {
          // 同步更新supplierName
          row.supplierName = getSupplierName(row.supplierId);
          // 更新合同名称下拉数据
          row.supplierNameMsg = '';
          row.contractId = '';
          row.contractName = '';
          row.contractNameMsg = '';
          row.materialId = '';
          row.materialCode = '';
          row.materialCodeMsg = '';
          row.materialName = '';
          row.materialSpec = '';
          row.unit = '';
          row.unitMsg = '';
          contractOptions.value = [];
          const suppliers = await getSupplierAndContracts(row.purchaseType);
          const supplier = suppliers.find(
            (item: any) => item.id === row.supplierId,
          );
          const contracts = supplier?.contracts || [];
          for (const contract of contracts) {
            contractOptions.value.push({
              label: contract.name,
              value: contract.id,
            });
          }
        },
      },
    },
  },
  {
    field: 'contractId',
    title: '合同名称',
    width: 150,
    formatter({ cellValue, row }: { cellValue: any; row: any }) {
      return getContractName(cellValue, row.contractName);
    },
    editRender: {
      name: 'VxeSelect',
      options: contractOptions,
      props: {
        filterable: true,
      },
      events: {
        change: async ({ row }: any) => {
          // 同步更新contractName
          row.contractName = getContractName(row.contractId);

          // 合同对应的供应商一定是唯一的
          const suppliers = await getSupplierAndContracts(row.purchaseType);
          for (const supplier of suppliers) {
            if (supplier.contracts) {
              for (const contract of supplier.contracts) {
                if (contract.id === row.contractId) {
                  row.supplierName = supplier.name;
                  row.supplierId = supplier.id;
                  break;
                }
              }
            }
          }

          // 更新材料下拉数据
          row.contractNameMsg = '';
          row.materialId = '';
          row.materialCode = '';
          row.materialCodeMsg = '';
          row.materialName = '';
          row.materialSpec = '';
          row.unit = '';
          row.unitMsg = '';
          const materials = await getMaterialDetails(row);
          refreshMaterialSelectionData(materials);
        },
      },
    },
  },
  {
    field: 'materialCode',
    title: '材料编码',
    width: 100,
    formatter({ cellValue }: { cellValue: any }) {
      return cellValue;
    },
    editRender: {
      name: 'VxeSelect',
      options: materialOptions,
      props: {
        filterable: true,
      },
      events: {
        change: async ({ row }: any) => {
          const materials = await getMaterialDetails(row);
          if (!_.isEmpty(materials)) {
            const material = materials?.find(
              (i) => i.code === row.materialCode,
            );
            row.materialId = material?.id;
            row.materialName = material?.name;
            row.materialSpec = material?.spec;
            row.unit = '';
            row.unitMsg = '';

            if (material) {
              const units = await getMaterialUnitSelectionData(
                row.contractId,
                row.materialId,
              );

              refreshMaterialUnitSelectionData(units || []);
              if (units && units.length === 1) {
                row.unit = units[0];
              }
            }
          }
        },
      },
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    width: 100,
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: 100,
  },
  {
    field: 'qualityStandard',
    title: '质量标准',
    width: 150,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入质量标准',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: 80,
    editRender: {
      name: 'VxeSelect',
      options: unitOptions,
      props: {
        filterable: true,
      },
    },
  },
  {
    field: 'siteEntryQuantity',
    title: '进场数量',
    width: 100,
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实收数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    width: 100,
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实收数量',
        type: 'float',
        digits: 8,
        autoFill: false,
      },
    },
  },
  {
    field: 'appearanceDescription',
    title: '外观质量描述',
    width: 150,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: null,
  height: '100%',
  loading: false,
  columns,
  data: [],
  rowClassName: () => {},
  cellClassName({ row, column }: any) {
    const { field } = column;
    if (
      (field === 'supplierId' && row.supplierNameMsg) ||
      (field === 'contractId' && row.contractNameMsg) ||
      row[`${field}Msg`]
    ) {
      return 'text-red-500';
    }
  },
  tooltipConfig: {
    contentMethod: ({ column, row }: any) => {
      const { field } = column;
      if (field === 'supplierId' && row.supplierNameMsg) {
        return row.supplierNameMsg;
      }
      if (field === 'contractId' && row.contractNameMsg) {
        return row.contractNameMsg;
      }
      if (row[`${field}Msg`]) {
        return row[`${field}Msg`];
      }
    },
  },
  editRules: {
    siteEntryDate: [{ required: true, message: '必须填写' }],
    purchaseType: [{ required: true, message: '必须填写' }],
    supplierId: [{ required: true, message: '必须填写' }],
    contractId: [{ required: true, message: '必须填写' }],
    materialCode: [{ required: true, message: '必须填写' }],
    unit: [{ required: true, message: '必须填写' }],
    siteEntryQuantity: [{ required: true, message: '必须填写' }],
    actualQuantity: [{ required: true, message: '必须填写' }],
  },
});
</script>
