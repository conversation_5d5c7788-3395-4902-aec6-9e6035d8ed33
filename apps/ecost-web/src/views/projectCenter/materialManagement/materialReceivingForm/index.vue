<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <div class="area-left h-full pr-2">
        <TimeSelect
          ref="timeSelectEl"
          :time-data="timeSelectData"
          @select="timeSelect"
        />
      </div>
      <div class="area-right h-full flex-1 overflow-auto">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top>
            <div class="flex h-[48px] items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton
                  type="primary"
                  size="small"
                  @click="debounceAddData"
                  v-auth="actionPermissions.apCreate"
                >
                  新增
                </ElButton>
                <ElButton type="default" size="small" @click="filter">
                  重置筛选
                </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出单据
                  </ElButton>
                </div>
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出列表
                  </ElButton>
                </div>
              </div>
            </div>
          </template>

          <template #seq="{ row, $rowIndex }">
            <div v-if="!row.internal">{{ $rowIndex + 1 }}</div>
          </template>
          <template #code="{ row }">
            <div v-if="!row.internal">
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
            <div v-else class="font-bold">{{ row.code }}</div>
          </template>

          <template #purchaseType="{ row }">
            <div>
              {{ getPurchaseTypeLabel(row.purchaseType) }}
            </div>
          </template>

          <template #materialReceiptStatus="{ row }">
            <div
              :class="{
                'text-green-500':
                  row.materialReceiptStatus === MaterialReceiptStatus.RECEIVED,
                'text-red-500':
                  row.materialReceiptStatus ===
                  MaterialReceiptStatus.UN_RECEIVED,
              }"
            >
              {{ getMaterialReceiptStatusLabel(row.materialReceiptStatus) }}
            </div>
          </template>

          <template #materialSettlementStatus="{ row }">
            <div
              :class="{
                'text-green-500':
                  row.materialSettlementStatus ===
                  MaterialSettlementStatus.SETTLED,
                'text-red-500':
                  row.materialSettlementStatus ===
                  MaterialSettlementStatus.UN_SETTLED,
              }"
            >
              {{
                getMaterialSettlementStatusLabel(row.materialSettlementStatus)
              }}
            </div>
          </template>

          <template #createAt="{ row }">
            <div v-if="!row.internal">
              {{
                dayjs(`${row.year}/${row.month}/${row.day}`).format(
                  'YYYY-MM-DD',
                )
              }}
            </div>
          </template>

          <template #taxIncludedAmount="{ row }">
            <div class="font-bold text-orange-500">
              {{ amountFormat({ cellValue: row.taxIncludedAmount }) }}
            </div>
          </template>
          <template #taxExcludedAmount="{ row }">
            <div class="font-bold text-orange-500">
              {{ amountFormat({ cellValue: row.taxExcludedAmount }) }}
            </div>
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.code" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.code" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </VxeGrid>
      </div>
    </div>

    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractMove"
      @refresh="drawerRefresh"
    />
  </Page>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import Big from 'big.js';
import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElPopover } from 'element-plus';
import _ from 'lodash';
import QrcodeVue from 'qrcode.vue';

import {
  addMaterialReceing,
  changeAuditStatus,
  delInspectionBill,
  getInspectionBillList,
  getTimeList,
} from '#/api/enterpriseCenter/materialReceivingForm/materialReceivingForm';
import TimeSelect from '#/components/TimeSelect/TimeSelect.vue';
import { usePageReturnState } from '#/store';
import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getMaterialReceiptStatusLabel,
  getMaterialSettlementStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  MaterialReceiptStatus,
  MaterialSettlementStatus,
  materialSettlementStatusOption,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import {
  amountFormat,
  findNextOrPrevRow,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';

defineOptions({
  name: 'MaterialEntryCheck',
});

const { actionPermissions } = getCurrentPremission();
const route = useRoute();
const pageReturnState = usePageReturnState();
const timeSelectData = ref([]);

// 新增弹窗是否展示
const addFromDialogVisible = ref(false);

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

const timeSelectEl = ref();
const tableRef = ref();
// 新增/编辑表单
const addOrEditContractForm = ref<any>({
  id: null,
  parentId: null,

  name: '',
  code: '',
  partyA: '',
  partyB: '',
  contractTemplateId: '',
  partyBType: '',
  proposedStatus: '',
});

// 当前选择的时间
const currentTimeItem = ref();

// 当前点击项
const currentItem = ref<any>();

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    fixed: 'left',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialSettlementStatus',
    title: '结算状态',
    width: '80',
    fixed: 'left',
    slots: {
      default: 'materialSettlementStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: materialSettlementStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择结算状态',
      },
    },
  },
  {
    field: 'code',
    title: '单据编码',
    width: '128',
    fixed: 'left',
    slots: {
      default: 'code',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '80',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: '180',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    width: '180',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '200',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'taxIncludedAmount',
    title: '含税金额',
    width: '140',
    slots: {
      default: 'taxIncludedAmount',
      footer: 'taxIncludedAmount',
    },
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    width: '140',
    slots: {
      default: 'taxExcludedAmount',
      footer: 'taxExcludedAmount',
    },
  },
  {
    field: 'creator',
    title: '编制人',
    width: '120',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
  },
  {
    field: 'createAt',
    title: '收料日期',
    width: '120',
    slots: {
      default: 'createAt',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '100',
    editRender: {
      name: 'VxeSelect',
      options: auditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
  {
    title: '二维码',
    width: '100',
    slots: {
      default: 'qrcode',
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: {
    transform: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled = !actionPermissions.apDelete;
            break;
          }

          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      if (row.internal) return;

      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前无修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    { id: null, code: '合计', taxIncludedAmount: 0, taxExcludedAmount: 0 },
  ],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionBill(id);
        if (res) {
          await refreshData();
          await getTimeSelectData();
          ElMessage.success('删除成功');
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = auditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );

      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await changeAuditStatus(id, row.auditStatus);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
  // 完成筛选
  filterChange() {
    countTotal(true);
  },
};
// 新增数据
async function addData() {
  const res = await addMaterialReceing();
  const itemData = {
    id: res.id,
    orgName: res.projectName,
    auditStatus: res.auditStatus,
    code: res.code,
    creator: res.creator,
    day: res.day,
    month: res.month,
    year: res.year,
    purchaseType: res.purchaseType,
    submitStatus: res.submitStatus,
    materialReceiptStatus: res.materialReceiptStatus,

    supplierId: '',
    supplierName: '',
    contractId: '',
    contractName: '',
  };
  infoData.value = itemData;
  currentItem.value = itemData;
  await refreshData();
  await getTimeSelectData();
  drawerVisible.value = true;
}
async function filter() {
  const $grid = tableRef.value;
  resetFilter(tableOptions.columns, $grid);
}

const debounceAddData = _.debounce(addData, 500);

// 打开详情
async function openDetail(row: any, isOpen = true) {
  infoData.value = {
    id: row.id,
    orgName: row.projectName,
    auditStatus: row.auditStatus,
    code: row.code,
    creator: row.creator,
    day: row.day,
    month: row.month,
    year: row.year,

    purchaseType: row.purchaseType,
    submitStatus: row.submitStatus,
    materialReceiptStatus: row.materialReceiptStatus,

    supplierId: row.supplierId,
    supplierName: row.supplierName,
    contractId: row.contractId,
    contractName: row.contractName,
  };
  if (isOpen) {
    drawerVisible.value = true;
  }
}

// 刷新
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

// 时间选择
async function timeSelect(row: any) {
  currentTimeItem.value = row;
  await getList();
}

// 合同选中移动
async function contractMove(move: number) {
  const $grid = tableRef.value;
  const targetItem = findNextOrPrevRow($grid, move);

  if (targetItem) {
    tableEvents.cellClick({ row: targetItem });
    setCurrentRow(tableOptions.data, tableRef.value, targetItem);
    openDetail(targetItem, false);
  }
}

// 获取时间选择框数据 并展开
async function getTimeSelectData() {
  const res = await getTimeList();
  timeSelectData.value = res;
}
// 初始化
async function init() {
  await getTimeSelectData();
  await getList();
}
// 获取物资进场验收数据
async function getList() {
  tableOptions.loading = true;

  const params: any = {};
  if (currentTimeItem.value?.year) {
    params.year = currentTimeItem.value.year;
  }
  if (currentTimeItem.value?.month) {
    params.month = currentTimeItem.value.month;
  }
  if (currentTimeItem.value?.day) {
    params.day = currentTimeItem.value.day;
  }
  const res = await getInspectionBillList(params);

  tableOptions.data = res;
  tableOptions.loading = false;

  countTotal();
  openRouteByCode();
}
// 计算总价
function countTotal(isFilter = false) {
  const totalItem = tableOptions.footerData[0];
  let taxIncludedAmount = 0;
  let taxExcludedAmount = 0;
  const $grid = tableRef.value;
  if ($grid) {
    const { tableData } = $grid.getTableData();
    const targetData = isFilter ? tableData : tableOptions.data;
    targetData
      .filter((v: any) => !v.internal)
      .forEach((item: any) => {
        if (item.taxIncludedAmount) {
          taxIncludedAmount = Big(taxIncludedAmount)
            .plus(item.taxIncludedAmount)
            .toNumber();
        }
        if (item.taxExcludedAmount) {
          taxExcludedAmount = Big(taxExcludedAmount)
            .plus(item.taxExcludedAmount)
            .toNumber();
        }
      });
    totalItem.taxIncludedAmount = taxIncludedAmount;
    totalItem.taxExcludedAmount = taxExcludedAmount;
  }
}

// function handleDropDownItem(command: string) {
//   if (command === 'download') {
//     // TODO 模版下载
//     downloadLocalFile('/file/费用字典发布.xlsx', '费用字典发布.xlsx');
//   }
// }

// 侧栏关闭后的刷新
async function drawerRefresh() {
  refreshData();
  getTimeSelectData();
}

// 退货单跳转
function openRouteByCode() {
  if (route.query.code) {
    const target = tableOptions.data.find(
      (v: any) => v.code === route.query.code,
    );
    setTimeout(() => {
      tableRef.value.scrollToRow(target);
    }, 1000);
    currentItem.value = target;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
    openDetail(target);
    route.query.code = '';
  }
}

// 打开存储的页面
function openRoutePage() {
  if (
    route.name === pageReturnState.fromPage &&
    pageReturnState.isDialogOpen &&
    pageReturnState.recordId &&
    tableRef.value
  ) {
    const target = tableOptions.data.find(
      (v: any) => v.id === pageReturnState.recordId,
    );
    setTimeout(() => {
      tableRef.value.scrollToRow(target);
    }, 1000);
    currentItem.value = target;

    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
    openDetail(currentItem.value);

    pageReturnState.reset(); // 清空
  }
}

// 渲染前
onMounted(async () => {
  await init();
  openRoutePage();
});
</script>

<style lang="scss" scoped>
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
