<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton
                  type="primary"
                  size="default"
                  @click="insureSubmit"
                  v-auth="actionPermissions.apUpdate"
                >
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="prevBtn"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="nextBtn"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              收料单 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="收料单" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-70px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="top-form grid grid-cols-3 gap-x-20 gap-y-1 pb-1 pt-3"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formData.orgName }}
              </ElFormItem>
              <!-- <ElFormItem
                  label="采购类型："
                  label-width="120px"
                  size="large"
                  label-position="left"
                  required
                >
                  {{ getPurchaseTypeLabel(formData.purchaseType) }}
                </ElFormItem> -->
              <ElFormItem
                label="采购类型："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElSelect
                  size="large"
                  v-model="formData.purchaseType"
                  placeholder=""
                  @change="purchaseypeChange"
                  filterable
                  :disabled="!localEditable || onlyFromEntryOrder"
                >
                  <ElOption
                    v-for="v in purchaseTypeLabelOption"
                    :key="v.value"
                    :label="v.label"
                    :value="v.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="供应商名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.supplierName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.supplierId"
                      placeholder=""
                      clearable
                      :disabled="!localEditable || !actionPermissions.apUpdate"
                      filterable
                      @change="supplierChange"
                      @clear="supplierClear"
                    >
                      <ElOption
                        v-for="v in supplierOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="合同名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.contractName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.contractId"
                      placeholder=""
                      clearable
                      :disabled="
                        formData.purchaseType ===
                          PurchaseType.PARTY_A_SUPPLIED ||
                        formData.purchaseType === PurchaseType.TRANSFER_IN ||
                        formData.supplierName === '零星材料供应商' ||
                        !localEditable ||
                        !actionPermissions.apUpdate
                      "
                      filterable
                      @change="contractChange"
                      @clear="contractClear"
                    >
                      <ElOption
                        v-for="v in contractOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="收料日期："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.entryDate"
                  type="date"
                  placeholder=""
                  @change="entryDateChange"
                  :clearable="false"
                  :disabled="
                    !entryDateEdit ||
                    !localEditable ||
                    !actionPermissions.apUpdate
                  "
                />
              </ElFormItem>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <!-- <template #seq="{ row, $rowIndex }">
                  <div v-if="row.id">{{ $rowIndex + 1 }}</div>
                </template> -->
                <template #materialName="{ row }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div>{{ row.materialName }}</div>
                      <div v-if="row.internal">
                        <ElButton
                          size="small"
                          @click="transferDataClick"
                          :disabled="!localEditable"
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
                <template #actualQuantity="{ row }">
                  <ElButton
                    v-if="!row.internal"
                    type="text"
                    size="small"
                    @click="traceActualQuanity(row)"
                  >
                    {{ row.actualQuantity }}
                  </ElButton>
                </template>

                <template #taxIncludedAmount="{ row }">
                  <div class="font-bold text-orange-500">
                    {{ amountFormat({ cellValue: row.taxIncludedAmount }) }}
                  </div>
                </template>
                <template #taxExcludedAmount="{ row }">
                  <div class="font-bold text-orange-500">
                    {{ amountFormat({ cellValue: row.taxExcludedAmount }) }}
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-3 gap-x-20 pt-2">
              <ElFormItem
                label="材料主管："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="经办人："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="验收人："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>

        <ExtrasPanel
          v-model:open-status="extrasOpen"
          v-model:cur-tab="extrasTab"
          :info-data="localInfoData"
          :file-list="fileList"
          :trace-list="traceList"
          :trace-columns="traceColumns"
          :visible-option="['ANNEX', 'TRACERECORD']"
          :editable="localEditable"
          list-type="picture"
          @del-annex="removeAnnex"
          @success-annex="addAnnex"
          @del-trace-record="delTraceBack"
          @jump-trace-record="jumpTraceRecord"
        />
      </div>

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="选择材料"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import type { PurchaseTypeEnum } from '#/types/materialManagement';

import {
  computed,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import Big from 'big.js';
import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addMaterialReceivingAttachment,
  changeSubmitStatus,
  delAttachmen,
  delInspectionDetail,
  delRecord,
  editInspectionDetail,
  editMaterialReceing,
  getAttachmentList,
  getContractlList,
  getInspectionDetailList,
  getRecordList,
  getSupplierlList,
  moveInspectionDetail,
} from '#/api/enterpriseCenter/materialReceivingForm/materialReceivingForm';
import {
  getIsEditParams,
  ParamsNamesType,
} from '#/api/systemManagementApi/orgParams';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import { usePageReturnState } from '#/store';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  PurchaseType,
  purchaseTypeLabelOption,
  SubmitStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import { amountFormat, setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import { TabEnum } from '../type';
import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();
const pageReturnState = usePageReturnState();
const route = useRoute();
const router = useRouter();
const { actionPermissions } = getCurrentPremission();
const extrasOpen = ref(false);
const extrasTab = ref('ANNEX');
// 收料日期是否可以编辑
const entryDateEdit = ref(false);
// 收料单数据只能来源于进场验收单
const onlyFromEntryOrder = ref(false);

// 附件数据
const fileList = ref([]);
// 数据溯源数据
const traceList = ref();

const traceColumns = [
  {
    field: 'name',
    title: '单据名称',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'entryNums',
    title: '进场数量',
    width: '60',
  },
  {
    field: 'actualNums',
    title: '实收数量',
    width: '60',
  },
];

// 供应商和合同名称的总数据
const contractAndSupplierOptions = ref<any>([]);

// 来自进场验收单的所有供应商
let entryCheckAllSupplierData: any[] = [];
// 来自进场验收单的所有合同
let entryCheckAllContractData: any[] = [];

// 供应商选项
const supplierOptions = ref<any>([]);
// 合同选项
const contractOptions = ref<any>([]);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);

// 表单数据
interface FormDataType {
  id: string;
  orgName: string;
  purchaseType: PurchaseTypeEnum | string;
  code: string;
  supplierId: string;
  supplierName: string;
  contractId: string;
  contractName: string;
  entryDate: string;
}

const formData = ref<FormDataType>({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
const oldFormData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval: any) => {
    localInfoData.value = nval;

    // addMaterialinfoData.value.receivingId = formData.value.id;

    // 重置数据溯源的数据
    traceList.value = []; // 重置数据溯源的数据

    // 调用获取材料清单列表
    await getList();
    // 调用获取附件列表
    await getAnnexlist();
    // 调用获取供应商名称
    await getSupplierContractlList();

    const { year, day, month, supplierId, creator } = nval;
    const form = {
      id: nval.id,
      orgName: nval.orgName,
      supplierId: nval.supplierId,
      contractId: nval.contractId,
      purchaseType: nval.purchaseType || '',
      code: nval.code,
      supplierName: nval.supplierName,
      contractName: nval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
    formData.value = form;
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };

    // 根据供应商Id 给 合同名称Id 赋值
    if (!onlyFromEntryOrder.value) {
      if (supplierId) {
        const data = contractAndSupplierOptions.value.find(
          (item: any) => item.id === formData.value.supplierId,
        );
        contractOptions.value = data?.contracts
          ? data.contracts.map((item: any) => {
              return {
                label: item.name,
                value: item.id,
              };
            })
          : [];
      } else {
        contractOptions.value = [];
      }
    }
  },
);

const localEditable = computed(() => {
  return (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    actionPermissions.apUpdate
  );
});

watch(
  () => formData,
  (nval, oval: any) => {
    const { year, day, month } = oval;
    oldFormData.value = {
      id: oval.id,
      orgName: oval.orgName,
      supplierId: oval.supplierId,
      contractId: oval.contractId,
      purchaseType: oval.purchaseType || '',
      code: oval.code,
      supplierName: oval.supplierName,
      contractName: oval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
  },
);

// 采购类型改变
const purchaseypeChange = async () => {
  if (tableOptions.data.length > 2) {
    // 还原为旧值
    nextTick(() => {
      formData.value.purchaseType = oldFormData.value.purchaseType;
    });

    ElMessage.warning('请先删除材料数据后再修改');
    return;
  }

  if (!onlyFromEntryOrder.value) {
    formData.value.supplierId = '';
    formData.value.supplierName = '';

    formData.value.contractId = '';
    formData.value.contractName = '';

    supplierOptions.value = [];
    contractOptions.value = [];

    await getSupplierContractlList();

    if (supplierOptions.value.length === 1) {
      formData.value.supplierId = supplierOptions.value[0].value;
      formData.value.supplierName = supplierOptions.value[0].label;
    } else {
      formData.value.supplierId = '';
      formData.value.supplierName = '';
    }

    await insureSave();

    await getList();
  }
};
// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 内置节点数据
const addBtnBlockItem = {
  id: null,
  name: null,
  internal: true, // 代表是内置节点
};

// 税率
let taxRate: null | number;

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);

const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: '',
    width: '50',
    // slots: {
    //   default: 'seq',
    // },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '140',
    slots: {
      default: 'materialName',
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'qualityStandard',
    title: '质量标准',
    minWidth: '80',
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '100',
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    minWidth: '100',
    slots: {
      default: 'actualQuantity',
    },
  },
  {
    field: 'inventoryQuantity',
    title: '库存数量',
    minWidth: '100',
    // slots: {
    //   default: 'inventoryQuantity',
    // },
  },
  {
    field: 'priceIncludingTax',
    title: '含税单价',
    minWidth: '100',

    editRender: {
      enabled: false,
      name: 'VxeNumberInput',
      // props: {
      //   placeholder: '请输入含税单价',
      //   type: 'float',
      //   digits: 100,
      //   autoFill: false,
      // },
    },
  },
  {
    field: 'priceExcludingTax',
    title: '单价',
    minWidth: '100',
    editRender: {
      enabled: false,
      name: 'VxeNumberInput',

      // props: {
      //   placeholder: '请输入单价',
      //   type: 'float',
      //   digits: 100,
      //   autoFill: false,
      // },
    },
  },
  {
    field: 'taxIncludedAmount',
    title: '含税金额',
    minWidth: '100',

    slots: {
      default: 'taxIncludedAmount',
      footer: 'taxIncludedAmount',
    },
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    minWidth: '100',

    slots: {
      default: 'taxExcludedAmount',
      footer: 'taxExcludedAmount',
    },
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: '60',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入外观质量描述',
      },
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  cellClassName: ({ row, column }: any) => {
    return !column.editRender ||
      column.editRender.enabled === false ||
      row.internal
      ? 'bg-gray-100'
      : '';
  },
  editRules: {
    priceIncludingTax: [
      {
        required: true,
        message: '请输入小数位<=6位的含税单价',
        pattern: /^\d+(\.\d{1,6})?$/,
      },
    ],
    priceExcludingTax: [
      {
        required: true,
        message: '请输入小数位<=6位的单价',
        pattern: /^\d+(\.\d{1,6})?$/,
      },
    ],
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',

            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, rowIndex, row }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            item.disabled = rowIndex === tableOptions.data.length - 2;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = rowIndex === 0;
            break;
          }
        }
        if (!localEditable.value) {
          item.disabled = true;
        }
      });
      return !!row.id;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row, column }: any) {
      // 如果是内置数据则不可修改
      if (row.internal) {
        return false;
      }
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前没有修改权限');
        return;
      }

      if (
        row.priceType === '固定单价' &&
        (column.field === 'priceExcludingTax' ||
          column.field === 'priceIncludingTax')
      ) {
        ElMessage.warning('固定单价不可修改');
        return;
      }

      // 版本数据已启用无法进行编辑
      if (!localEditable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    {
      id: null,
      materialName: '合计',
      internal: true,

      priceExcludingTax: '',
      priceIncludingTax: '',
      taxExcludedAmount: 0,
      taxIncludedAmount: 0,
    },
  ],
});

const isPurchaseTypeOne = ref<boolean>(false);
watch(
  () => formData.value.purchaseType,
  (val) => {
    isPurchaseTypeOne.value =
      val === PurchaseType.CENTRALIZED_PURCHASE ||
      val === PurchaseType.PARTY_A_DIRECTED ||
      val === PurchaseType.SELF_PURCHASE;

    tableOptions.columns.forEach((item: any) => {
      if (item.field === 'priceIncludingTax') {
        item.editRender.enabled = isPurchaseTypeOne.value;
      }
      if (item.field === 'priceExcludingTax') {
        item.editRender.enabled = isPurchaseTypeOne.value;
      }
    });
  },
  { immediate: true },
);

// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({
    menu,
    rowIndex,
    row,
  }: {
    menu: any;
    row: any;
    rowIndex: any;
  }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionDetail(id);
        if (res) {
          await refreshData();
          ElMessage.success('删除成功');

          // 计算统计数据
          countTotalPrice();

          // 关闭侧边栏同时清空数据
          extrasOpen.value = false;
          traceList.value = []; // 重置数据溯源的数据
        }
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex + 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
    if (menu.code === 'MOVE_UP') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex - 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    const errMsg = await tableRef.value.validate(row);
    if (errMsg) return;

    // 如果是含税单价反算 单价
    if (column.field === 'priceIncludingTax' && taxRate) {
      const rate = Big(taxRate).plus(1);
      row.priceExcludingTax = Big(row.priceIncludingTax)
        .div(rate)
        .round(6, Big.roundHalfUp)
        .toNumber();
    }
    // 如果是单价反算 含税单价
    if (column.field === 'priceExcludingTax' && taxRate) {
      const rate = Big(taxRate).plus(1);
      row.priceIncludingTax = Big(row.priceExcludingTax)
        .times(rate)
        .round(6, Big.roundHalfUp)
        .toNumber();
    }

    // 计算统计数据
    countTotalPrice();

    const data = {
      receivingId: localInfoData.value.id,
      priceExcludingTax: row.priceExcludingTax,
      priceIncludingTax: row.priceIncludingTax,
      taxExcludedAmount: row.taxExcludedAmount,
      taxIncludedAmount: row.taxIncludedAmount,
      remark: row.remark,
    };
    const res = await editInspectionDetail(row.id, data);
    if (res) {
      refreshData();
    }
  },
};

// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }

  if (localInfoData.value.submitStatus === SubmitStatus.PENDING) {
    // 需要提交数据了
    const isPassForm = checkForm();
    if (!isPassForm) {
      return;
    }

    if (tableOptions.data.length <= 1) {
      ElMessage.warning('请添加验收材料');
      return;
    }
  }

  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const { id } = formData.value;

  const totalItem = tableOptions.data[0];

  const data = {
    taxExcludedAmount: totalItem.taxExcludedAmount, // 不含税金额
    taxIncludedAmount: totalItem.taxIncludedAmount, // 含税金额
    submitStatus, // 修改提交状态为已提交
  };

  const res = await changeSubmitStatus(id, data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

// 确认修改
async function insureSave() {
  const year = Number(dayjs(formData.value.entryDate).format('YYYY'));
  const month = Number(dayjs(formData.value.entryDate).format('M'));
  const day = Number(dayjs(formData.value.entryDate).format('D'));

  const params = {
    purchaseType: formData.value.purchaseType as PurchaseTypeEnum,
    supplierId: formData.value.supplierId,
    supplierName: formData.value.supplierName,
    contractId: formData.value.contractId,
    contractName: formData.value.contractName,

    year,
    month,
    day,
  };

  await editMaterialReceing(formData.value.id, params);
}

// 供应商改变
async function supplierChange() {
  // 清单中有数据则不可修改
  if (tableOptions.data.length > 2) {
    // 还原为旧值
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;
    });

    ElMessage.warning('请先删除材料数据后再修改');

    return;
  }

  // 根据 数据是否只能来源于进场验收单 区分处理
  if (onlyFromEntryOrder.value) {
    // 筛选出合同的数据
    contractOptions.value = entryCheckAllContractData.filter(
      (v: any) => formData.value.supplierId === v.supplierId,
    );
    // 同时给名称赋值
    const data = supplierOptions.value.find(
      (item: any) => item.value === formData.value.supplierId,
    );
    formData.value.supplierName = data.label;
    // 给采购类型赋值
    formData.value.purchaseType = data.purchaseType;
  } else {
    // 同时给名称赋值
    const data = contractAndSupplierOptions.value.find(
      (item: any) => item.id === formData.value.supplierId,
    );
    formData.value.supplierName = data.name;

    // 筛选出合同的数据
    contractOptions.value = data?.contracts
      ? data.contracts.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        })
      : [];
  }

  if (contractOptions.value.length === 1) {
    formData.value.contractId = contractOptions.value[0].value;
    formData.value.contractName = contractOptions.value[0].label;
  } else {
    formData.value.contractId = '';
    formData.value.contractName = '';
  }

  await insureSave();
}

// 合同改变
async function contractChange() {
  // 清单中有树据则不可修改

  if (tableOptions.data.length > 2) {
    // 还原为旧值
    nextTick(() => {
      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;
    });
    ElMessage.warning('请先删除材料数据后再修改');
    return;
  }

  if (onlyFromEntryOrder.value) {
    // 合同改变 先找出合同的数据  再通过合同的supplierId 筛选出符合的所有供应商
    const contract = entryCheckAllContractData.find(
      (v: any) => formData.value.contractId === v.value,
    );

    supplierOptions.value = entryCheckAllSupplierData.filter(
      (v: any) => contract.supplierId === v.value,
    );

    formData.value.supplierId = '';
    formData.value.supplierName = '';
    if (supplierOptions.value.length === 1) {
      formData.value.supplierId = supplierOptions.value[0].value;
      formData.value.supplierName = supplierOptions.value[0].label;
    }
    // 同时给名称赋值
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );
    formData.value.contractName = data.label;
    // 给采购类型赋值
    formData.value.purchaseType = data.purchaseType;
  } else {
    // 同时给名称赋值
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );
    formData.value.contractName = data.label;
  }

  await insureSave();
}

// 收料日期改变
async function entryDateChange() {
  await insureSave();
}

async function supplierClear() {
  resetData();
}

async function contractClear() {
  resetData();
}

async function resetData() {
  supplierOptions.value = entryCheckAllSupplierData;
  contractOptions.value = entryCheckAllContractData;

  formData.value.supplierId = '';
  formData.value.supplierName = '';
  formData.value.contractId = '';
  formData.value.contractName = '';

  formData.value.purchaseType = '';
}

// 计算合计的含税单价 和 单价
function countTotalPrice() {
  const totalItem = tableOptions.footerData[0];
  let priceExcludingTax = 0;
  let priceIncludingTax = 0;

  let taxExcludedAmount = 0;
  let taxIncludedAmount = 0;

  const filterData = tableOptions.data.filter((item: any) => !item.internal);

  filterData.forEach((item: any) => {
    // 总单价
    if (item.priceExcludingTax) {
      priceExcludingTax = Big(priceExcludingTax)
        .plus(item.priceExcludingTax)
        .toNumber();
    }
    // 总含税单价
    if (item.priceIncludingTax) {
      priceIncludingTax = Big(priceIncludingTax)
        .plus(item.priceIncludingTax)
        .toNumber();
    }

    // 含税金额 = 实收数量 * 含税单价
    if (item.actualQuantity && item.priceIncludingTax) {
      item.taxIncludedAmount = Big(item.actualQuantity)
        .times(item.priceIncludingTax)
        .round(6, Big.roundHalfUp)
        .toNumber();
    }
    // 金额 = 实收数量 * 不含税单价
    if (item.actualQuantity && item.priceExcludingTax) {
      item.taxExcludedAmount = Big(item.actualQuantity)
        .times(item.priceExcludingTax)
        .round(6, Big.roundHalfUp)
        .toNumber();
    }

    // 总含税金额
    if (item.taxIncludedAmount) {
      taxIncludedAmount = Big(taxIncludedAmount)
        .plus(item.taxIncludedAmount)
        .toNumber();
    }
    // 总金额
    if (item.taxExcludedAmount) {
      taxExcludedAmount = Big(taxExcludedAmount)
        .plus(item.taxExcludedAmount)
        .toNumber();
    }
  });

  // totalItem.priceExcludingTax = priceExcludingTax;
  // totalItem.priceIncludingTax = priceIncludingTax;
  totalItem.taxExcludedAmount = taxExcludedAmount;
  totalItem.taxIncludedAmount = taxIncludedAmount;
}

// 弹窗打开回调
async function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
  emit('refresh');
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);

// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getInspectionDetailList(id);
  const tableData = res.list;
  taxRate = res.taxRate;
  tableOptions.data =
    tableData.length === 0
      ? [addBtnBlockItem]
      : [...tableData, addBtnBlockItem];

  countTotalPrice();
}

// 点击穿梭框
async function transferDataClick() {
  if (!checkForm()) {
    return;
  }

  // const isPARTY_A_SUPPLIED =
  //   formData.value.purchaseType === PurchaseType.PARTY_A_SUPPLIED;
  // const isTRANSFER_IN =
  //   formData.value.purchaseType === PurchaseType.TRANSFER_IN;
  // const isLXSUPPLIED = formData.value.supplierName === '零星材料供应商';

  // // 甲供 调拨 还有 甲指 中的零星供应商 是 选择材料字典 否则是合同材料
  // curTab.value =
  //   isPARTY_A_SUPPLIED || isTRANSFER_IN || isLXSUPPLIED
  //     ? 'MATERIAL_DICT'
  //     : 'CONTRACT';
  curTab.value = TabEnum.MATERIAL_ENTRY;

  addMaterialinfoData.value = {
    receivingId: formData.value.id,
    materialSearchType: curTab.value,
    purchaseType: formData.value.purchaseType,
  };

  addOrEditMaterialVisible.value = true;
}

function checkForm() {
  // 如果为甲供必须选择供应商名称
  if (
    formData.value.purchaseType === PurchaseType.TRANSFER_IN &&
    !formData.value.supplierId
  ) {
    ElMessage.warning('请选择供应商名称');
    return false;
  }
  // 如果为 甲指/自采/集采 必须选择供应商名称 和 合同名称 除非
  if (isPurchaseTypeOne.value) {
    if (!formData.value.supplierId) {
      ElMessage.warning('请选择供应商名称');
      return false;
    }

    if (
      formData.value.supplierName !== '零星材料供应商' &&
      !formData.value.contractId
    ) {
      ElMessage.warning('请选择合同名称');
      return false;
    }
  }

  return true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}

// 获取供应商名称
async function getSupplierContractlList() {
  await getSupplierOption();
  await getContractlOption();
  // 如果数据强制来源于进场验收单则调用与进场验收单审批过的数据 否则 则是所有数据
  // if (onlyFromEntryOrder.value) {
  //   await getSupplierOption();
  //   await getContractlOption();
  // } else {
  //   const params = {
  //     purchaseType: formData.value.purchaseType as PurchaseTypeEnum,
  //   };
  //   const res = await getSupplierAndContractlList(params);
  //   contractAndSupplierOptions.value = res;
  //   supplierOptions.value = res.map((item: any) => {
  //     return {
  //       label: item.name,
  //       value: item.id,
  //     };
  //   });
  // }
}

// 获取供应商
async function getSupplierOption() {
  const supplierRes = await getSupplierlList();
  entryCheckAllSupplierData = supplierRes.map((item: any) => {
    return {
      contractId: item.contractId,
      purchaseType: item.purchaseType,

      label: item.supplierName,
      value: item.supplierId,
    };
  });

  supplierOptions.value = entryCheckAllSupplierData;
}

// 获取合同
async function getContractlOption() {
  const ContractRes = await getContractlList();
  entryCheckAllContractData = ContractRes.map((item: any) => {
    return {
      supplierId: item.supplierId,
      purchaseType: item.purchaseType,

      label: item.contractName,
      value: item.contractId,
    };
  });

  contractOptions.value = entryCheckAllContractData;
}

// 新增附件
async function addAnnex(data: any) {
  const _data = {
    receivingId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addMaterialReceivingAttachment(_data);
  if (res) {
    ElMessage.success('添加成功');
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;

  const res = await delAttachmen(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}
// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}

// 获取数据溯源数据
async function traceActualQuanity(row: any) {
  const res = await getRecordList(row.id);

  traceList.value = res.map((item: any) => {
    return {
      name: item.code,
      unit: item.unit,
      entryNums: item.siteEntryQuantity,
      actualNums: item.actualQuantity,
      ...item,
    };
  });

  extrasOpen.value = true;
  extrasTab.value = 'TRACERECORD';
}

// 删除数据溯源
async function delTraceBack({ row }: any) {
  const id = row.id;
  const res = await delRecord(id);
  if (res) {
    ElMessage.success('删除成功');

    traceList.value = traceList.value.filter((v: any) => v.id !== id);
    refreshData();
  }
}

// 数据溯源跳转
async function jumpTraceRecord({ row }: any) {
  const routeName = route.name as string;
  pageReturnState.setReturnState({
    fromPage: routeName,
    recordId: localInfoData.value?.id ?? '',
    isDialogOpen: true,
  });
  router.push({ name: 'MenuMaterialEntryCheck', query: { code: row.code } });
}

async function init() {
  const paramsNames = `${ParamsNamesType.BASE_ACCOUNT},${ParamsNamesType.MATERIAL_RECEIVING}`;
  const res = await getIsEditParams(paramsNames);
  const isEntryDateEdit = res[ParamsNamesType.BASE_ACCOUNT];
  const isOnlyFromEntryOrder = res[ParamsNamesType.MATERIAL_RECEIVING];

  onlyFromEntryOrder.value = isOnlyFromEntryOrder.isMaterialIncomingInspection;
  entryDateEdit.value = isEntryDateEdit.modifyAccountTime;
}

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

onBeforeMount(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 12px;
}
</style>
