<template>
  <div class="selections h-full">
    <div class="flex h-[30px] justify-between text-[14px]">
      <ElRadioGroup v-model="curTab">
        <ElRadio
          v-for="v in tabOptions"
          :key="v.value"
          :value="v.value"
          :disabled="v.disabled"
          size="small"
        >
          {{ v.label }}
        </ElRadio>
      </ElRadioGroup>
    </div>
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <!-- <template #code="{ row }">
              <ElButton type="text" size="small">{{ row.code }}</ElButton>
            </template> -->
            <template #purchaseType="{ row }">
              <div>
                {{ getPurchaseTypeLabel(row.purchaseType) }}
              </div>
            </template>
            <template #submitStatus="{ row }">
              <div
                :class="{
                  'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                  'text-red-500': row.submitStatus === SubmitStatus.PENDING,
                }"
              >
                {{ getSubmitStatusLabel(row.submitStatus) }}
              </div>
            </template>
            <template #auditStatus="{ row }">
              <div
                :class="{
                  'text-red-500': row.auditStatus === AuditStatus.PENDING,
                  'text-orange-500':
                    row.auditStatus === AuditStatus.AUDITING ||
                    row.auditStatus === AuditStatus.REJECTED,
                  'text-green-500': row.auditStatus === AuditStatus.APPROVED,
                }"
              >
                {{ getAuditStatusLabel(row.auditStatus) }}
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, watch } from 'vue';

import { ElRadio, ElRadioGroup } from 'element-plus';

import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[]; // 已选的明细数据
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'selectAll', data: any): void;

  (e: 'add', data: any): void; // 添加到已选明细的 数据
}>();

const curTab = inject<any>('curTab'); // 当前选中的tab
const multiple = inject<any>('multiple'); // 是否是多选

// tab项
const tabOptions = ref([
  {
    label: '选择进场验收单材料',
    value: 'MATERIAL_ENTRY',
    disabled: true,
    visible: true,
  },
  { label: '选择合同材料', value: 'CONTRACT', disabled: true, visible: true },
]);
tabOptions.value.forEach((item) => {
  item.disabled = item.value !== curTab.value;
});

// 当前选中的分类表格数据
const currentClassItem = ref();
// 分类表格数据
const prevTableRef = ref();
// 分类表格配置
const prevColumns = [
  {
    type: 'seq',
    title: ' ',
    field: 'seq',
    width: '60',
  },
  {
    field: 'code',
    title: '单据编码',
    minWidth: '160',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '100',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'supplierName',
    title: '供应商',
    minWidth: '160',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    minWidth: '160',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'creator',
    title: '编制人',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
  },
  {
    field: 'incomingInspectionDate',
    title: '进场日期',
    width: '100',
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审核状态',
    width: '100',
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-300';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns: prevColumns,
  data: props.choiceClassData,
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = nval;
    if (prevTableOptions.data.length > 0) {
      const currentRow = prevTableOptions.data[0];
      currentClassItem.value = currentRow;
      setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow);
      emit('select', currentRow);
    }
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    currentClassItem.value = row;
    emit('select', row);
  },
  cellDblclick({ row }: any) {
    if (row.disabled || row.selected) {
      tableOptions.data = [];
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }
    currentClassItem.value = row;
    row.selected = true;

    emit('selectAll', row);
  },
};

// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    type: 'seq',
    title: ' ',
    field: 'seq',
    width: '60',
  },
  {
    field: 'materialCode',
    title: '材料编码',
    width: '120',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料编码',
      },
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: '80',
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'incomingUnit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'actualQuantity',
    title: '实收数量',
    width: '80',
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-300';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns,
  data: props.choiceDetailData,
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

// 已选择的类别数据
const selectionClass = inject<any>('selectionClass'); // 是否是多选
watch(
  () => selectionClass,
  () => {
    prevTableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = selectionClass.value.includes(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

// 明细表格数据状态修改
const selections = ref(props.selectionData); // 已选数据
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    let isAllSelected = tableOptions.data.length > 0; // 是否以及全选
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = multiple.value ? false : !!ids.has(id);

      item.selected = selected;
      if (!selected) {
        isAllSelected = false;
      }
    });

    if (
      isAllSelected &&
      !selectionClass.value.includes(currentClassItem.value?.id)
    ) {
      selectionClass.value.push(currentClassItem.value.id);
    }
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (row.selected || row.disabled) return;

    emit('add', row);
  },
};
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
