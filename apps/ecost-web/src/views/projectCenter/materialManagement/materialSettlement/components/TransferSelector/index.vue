<template>
  <div
    class="flex h-[540px] flex-col items-center justify-between"
    style="height: fit-content"
  >
    <!-- 上部确认区 -->
    <div class="h-full w-full">
      <Choices
        :choice-class-data="choiceClass"
        :choice-detail-data="choiceDetailData"
        :selection-data="selections"
        @select="choicesSelect"
        @select-all="selectAll"
        @add="choicesChange"
      />
    </div>
    <!-- 中部图标区 -->
    <div class="mt-2 flex items-center justify-center">
      <ElIcon size="26" style="transform: rotate(90deg)">
        <DArrowRight />
      </ElIcon>
    </div>
    <!-- 底部确认区 -->
    <div class="mt-[-20px] h-full w-full">
      <Selections
        :selection-data="selections"
        @select="choicesSelection"
        @remove="selectionChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { provide, ref, watch } from 'vue';

import { DArrowRight } from '@element-plus/icons-vue';
import { add } from '@ewing/infra-cloud-sdk/dist/common-utils';
import { ElIcon } from 'element-plus';

import { SettlementType } from '#/types/materialManagement';

import Choices from './components/LeftChoices.vue';
import Selections from './components/RightSelections.vue';

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    curTab?: string;
    multiple?: boolean;

    selectionData: any[];
  }>(),
  {
    selectionData: () => [],
    choiceClassData: () => [],
    choiceDetailData: () => [],

    curTab: SettlementType.PURCHASE,
    multiple: false,
  },
);

const emit = defineEmits<{
  (e: 'update:selectionData', selectionData: any[]): void;
  (e: 'selectAll', data: any): void;
  (e: 'select', data: any): void;
}>();

const localCurTab = ref();

watch(
  () => props.curTab,
  (nval) => {
    localCurTab.value = nval;
  },
  { deep: true, immediate: true },
);

provide('curTab', localCurTab);

// 选择区:分类
const choiceClass = ref<any[]>([]);
const currentClassItem = ref();
watch(
  () => props.choiceClassData,
  (nval) => {
    choiceClass.value = nval;
  },
  { deep: true, immediate: true },
);

// 已选的数据：明细
const selections = ref(props.selectionData);
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
  },
  { deep: true, immediate: true },
);

// 选中分类
async function choicesSelect(row: any) {
  currentClassItem.value = row;
  emit('select', row);
}
// 选中分类并添加全部数据
async function selectAll(row: any) {
  currentClassItem.value = row;

  emit('selectAll', row);
}

// 选中确认行
async function choicesSelection(row: any) {
  currentClassItem.value = null;
  row.isSelection = true;
  emit('select', row);
}

function getTotalAmount(newSelections: any[]) {
  const totalItem = newSelections[0];
  totalItem.taxExcludedAmount = 0;
  newSelections.forEach((item, index) => {
    if (index > 0) {
      totalItem.taxExcludedAmount = add(
        item.taxExcludedAmount,
        totalItem.taxExcludedAmount,
      )
        .toNumber()
        .toFixed(2);
    }
  });

  return newSelections;
}

// 选择器改变
async function choicesChange(row: any) {
  const newSelections = [...selections.value, row];
  selections.value = getTotalAmount(newSelections);
  emit('update:selectionData', newSelections);
}
// 已选择的数据改变
async function selectionChange({ row }: any) {
  const idx = selections.value.findIndex((v) => v.id === row.id);
  if (idx !== -1) {
    const curClass = choiceClass.value.find((v) => v.id === row.id);
    curClass.selected = false;

    const newSelections = selections.value.filter((item) => item.id !== row.id);
    selections.value = getTotalAmount(newSelections);
    emit('update:selectionData', newSelections);
  }
}
</script>
<style scoped lang="scss"></style>
