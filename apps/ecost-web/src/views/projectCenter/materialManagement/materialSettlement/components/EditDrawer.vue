<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="false"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton
                  type="primary"
                  size="default"
                  @click="insureSubmit"
                  v-auth="actionPermissions.apUpdate"
                >
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="prevBtn"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="nextBtn"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              物资采购结算单 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="物资结算单" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-70px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="top-form grid grid-cols-3 gap-x-20 gap-y-1 pb-1 pt-6"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formData.orgName }}
              </ElFormItem>
              <ElFormItem
                label=" "
                label-width="106px"
                size="large"
                label-position="left"
              />
              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="供应商名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.supplierName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.supplierId"
                      placeholder=""
                      clearable
                      :disabled="!localEditable || !actionPermissions.apUpdate"
                      filterable
                      @change="supplierChange"
                    >
                      <ElOption
                        v-for="v in supplierOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="合同名称："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.contractName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.contractId"
                      placeholder=""
                      clearable
                      :disabled="!localEditable || !actionPermissions.apUpdate"
                      filterable
                      @change="contractChange"
                    >
                      <ElOption
                        v-for="v in contractOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>
              <ElFormItem
                label="结算日期："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.settlementDate"
                  type="date"
                  placeholder=""
                  @change="entryDateChange"
                  :clearable="false"
                  :disabled="!localEditable || !actionPermissions.apUpdate"
                />
              </ElFormItem>
            </ElForm>

            <ElForm :model="formData" class="top-form">
              <ElRow :gutter="24">
                <ElCol :span="7">
                  <ElFormItem
                    label="本期不含税："
                    label-width="90px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{ amountFormatValue(formData.taxExcludedAmount) }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="7">
                  <ElFormItem
                    label="本期含税："
                    label-width="75px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{ amountFormatValue(formData.taxIncludedAmount) }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="6">
                  <ElFormItem
                    label="本期税金："
                    label-width="75px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{ amountFormatValue(formData.taxAmount) }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="4">
                  <ElFormItem
                    label="税率(%)："
                    label-width="70px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{
                      formData.taxRate
                        ? amountFormatValue(formData.taxRate * 100)
                        : null
                    }}
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElRow :gutter="24">
                <ElCol :span="7">
                  <ElFormItem
                    label="截至上期末累计含税："
                    label-width="145px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{
                      amountFormatValue(
                        formData.beforeCumulationTaxIncludedAmount,
                      )
                    }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="7">
                  <ElFormItem
                    label="截至本期末累计含税："
                    label-width="145px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{
                      formData.beforeCumulationTaxIncludedAmount ||
                      formData.taxIncludedAmount
                        ? amountFormatValue(
                            (formData.beforeCumulationTaxIncludedAmount || 0) +
                              (formData.taxIncludedAmount || 0),
                          )
                        : null
                    }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="6">
                  <ElFormItem
                    label="合同签订金额："
                    label-width="100px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{ amountFormatValue(formData.contractAmount) }}
                  </ElFormItem>
                </ElCol>
                <ElCol :span="4">
                  <ElFormItem
                    label="累计百分比："
                    label-width="90px"
                    size="large"
                    label-position="left"
                    class="bold-label"
                  >
                    {{
                      formData.contractAmount
                        ? amountFormatValue(
                            (((formData.beforeCumulationTaxIncludedAmount ||
                              0) +
                              (formData.taxIncludedAmount || 0)) /
                              formData.contractAmount) *
                              100,
                          )
                        : null
                    }}
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row, $rowIndex }">
                  <div v-if="row.id">{{ $rowIndex + 1 }}</div>
                </template>
                <template #materialName="{ row, $rowIndex }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div>{{ row.materialName }}</div>
                      <div
                        v-if="
                          ($rowIndex === 1 && tableOptions.data.length <= 0) ||
                          $rowIndex === tableOptions.data.length - 1
                        "
                      >
                        <ElButton
                          size="small"
                          @click="transferDataClick"
                          :disabled="!localEditable"
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
                <template #quantity="{ row }">
                  <ElButton
                    v-if="!row.internal"
                    type="text"
                    size="small"
                    @click="traceActualQuanity(row)"
                  >
                    {{ row.quantity }}
                  </ElButton>
                </template>

                <template #taxIncludedAmount="{ row, $rowIndex }">
                  <div :class="$rowIndex === 0 ? `text-l text-orange-500` : ''">
                    {{ amountFormat({ cellValue: row.taxIncludedAmount }) }}
                  </div>
                </template>
                <template #taxExcludedAmount="{ row, $rowIndex }">
                  <div :class="$rowIndex === 0 ? `text-l text-orange-500` : ''">
                    {{ amountFormat({ cellValue: row.taxExcludedAmount }) }}
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-2 gap-x-20 pt-2">
              <ElFormItem
                label="材料员:"
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="供货单位(盖章):"
                label-width="150px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="项目经理:"
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="合同授权人(签字):"
                label-width="150px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="成本核算中心(物资管理员):"
                label-width="200px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="68px"
                size="large"
                label-position="left"
              >
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>

        <ExtrasPanel
          v-model:open-status="extrasOpen"
          v-model:cur-tab="extrasTab"
          :info-data="localInfoData"
          :file-list="fileList"
          :trace-list="traceList"
          :trace-columns="traceColumns"
          :trace-detail-list="traceDetailList"
          :trace-detail-columns="traceDetailColumns"
          :is-trace-detail-tree="isTraceDetailTree"
          :visible-option="['ANNEX', 'TRACEDETAIL', 'TRACERECORD']"
          :editable="localEditable"
          list-type="picture"
          @del-annex="removeAnnex"
          @success-annex="addAnnex"
          @del-trace-record="delTraceBack"
          @jump-trace-record="jumpTraceRecord"
          @jump-trace-detail="jumpTraceRecord"
        >
          <template #traceRecordHeader v-if="localEditable">
            <div>已选择: {{ selectedInfo.selectedNum || 0 }}张</div>
            <div>
              未选择: {{ selectedInfo.noSelectedNum || 0 }}张(其中:已审批{{
                selectedInfo.noSelectedApprovedNum || 0
              }}张 未审批{{ selectedInfo.noSelectedOtherNum || 0 }}张)
            </div>
          </template>

          <template #traceDetailHeader>
            <div>{{ currentItem.materialName }}</div>
            <div>{{ currentItem.materialSpec }}</div>
          </template>
        </ExtrasPanel>
      </div>

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="选择收料单/退货单"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import { add } from '@ewing/infra-cloud-sdk/dist/common-utils';
import {
  ElButton,
  ElCol,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addMaterialReceivingAttachment,
  changeSubmitStatus,
  delAttachment,
  delRecord,
  editMaterialSettlement,
  getAttachmentList,
  getMaterialReceivingReturnDetails,
  getMaterialSettlementList,
  getReceivingReturnList,
  getSettlementDetails,
  getSupplierAndContractList,
  moveInspectionDetail,
  updateSettlementDetail,
} from '#/api/projectCenter/materialManagement/materialSettlement';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import { usePageReturnState } from '#/store';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  SettlementType,
  SubmitStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import {
  amountFormat,
  amountFormatValue,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();
const { actionPermissions } = getCurrentPremission();

const extrasOpen = ref(false);
const extrasTab = ref('ANNEX');

// 收料单数据只能来源于进场验收单
// const onlyFromEntryOrder = ref(false);

// 附件数据
const fileList = ref([]);
// 数据溯源数据
const traceList = ref();
const traceDetailList = ref();

const selectedInfo = reactive<any>({
  selectedNum: 0,
  noSelectedNum: 0,
  noSelectedApprovedNum: 0,
  noSelectedOtherNum: 0,
});

const traceColumns = [
  {
    field: 'name',
    title: '单据编码',
    minWidth: '160',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '100',
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'creator',
    title: '编制人',
    width: '100',
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
  },
  {
    field: 'billDate',
    title: '日期',
    width: '100',
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    formatter: ({ cellValue }: any) => {
      return getSubmitStatusLabel(cellValue);
    },
  },
  {
    field: 'auditStatus',
    title: '审核状态',
    width: '100',
    formatter: ({ cellValue }: any) => {
      return getAuditStatusLabel(cellValue);
    },
  },
];

const isTraceDetailTree = ref(false);
const traceDetailColumns = [
  {
    field: 'name',
    title: '单据编码',
    width: '160',
    treeNode: true,
    slots: {
      default: 'name',
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '100',
  },
  {
    field: 'quantity',
    title: '数量',
    width: '100',
  },
  {
    field: 'settlementPrice',
    title: '单价',
    width: '100',
    formatter: amountFormat,
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    width: '100',
    formatter: amountFormat,
  },
  {
    field: 'billDate',
    title: '日期',
    width: '100',
  },
  {
    field: 'creator',
    title: '编制人',
    width: '100',
  },
];

// 来自进场验收单的所有供应商
let entryCheckAllSupplierData: any[] = [];
// 来自进场验收单的所有合同
let entryCheckAllContractData: any[] = [];

// 供应商选项
const supplierOptions = ref<any>([]);
// 合同选项
const contractOptions = ref<any>([]);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);

// 表单数据
interface FormDataType {
  id: string;
  orgName: string;
  code: string;
  supplierId: string;
  supplierName: string;
  contractId: string;
  contractName: string;
  settlementDate: string;
  taxRate: null | number;
  priceType: null | string;
  contractAmount: null | number;
  taxExcludedAmount: null | number;
  taxIncludedAmount: null | number;
  taxAmount: null | number;
  beforeCumulationTaxIncludedAmount: null | number;
  beforeCumulationTaxExcludedAmount: null | number;
  beforeCumulationTaxAmount: null | number;
}

const formData = ref<FormDataType>({
  id: '',
  orgName: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  settlementDate: '',
  taxRate: null,
  priceType: null,
  contractAmount: null,
  taxExcludedAmount: null,
  taxIncludedAmount: null,
  taxAmount: null,
  beforeCumulationTaxIncludedAmount: null,
  beforeCumulationTaxExcludedAmount: null,
  beforeCumulationTaxAmount: null,
});
const oldFormData = ref({
  id: '',
  orgName: '',
  code: '',
  supplierId: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  settlementDate: '',
  taxRate: null,
  priceType: null,
  contractAmount: null,
  taxExcludedAmount: null,
  taxIncludedAmount: null,
  taxAmount: null,
  beforeCumulationTaxIncludedAmount: null,
  beforeCumulationTaxExcludedAmount: null,
  beforeCumulationTaxAmount: null,
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval: any) => {
    localInfoData.value = nval;

    // 重置数据溯源的数据
    traceList.value = []; // 重置数据溯源的数据
    traceDetailList.value = [];

    // 调用获取材料清单列表
    await getList();
    // 调用获取附件列表
    await getAnnexlist();
    // 调用获取供应商名称
    await getSupplierContractList();
    // 调用获取追溯单据列表
    await getTraceList();

    const { creator } = nval;
    const form = {
      id: nval.id,
      orgName: nval.orgName,
      supplierId: nval.supplierId,
      contractId: nval.contractId,
      code: nval.code,
      supplierName: nval.supplierName,
      contractName: nval.contractName,
      settlementDate: nval.settlementDate,
      taxRate: nval.taxRate,
      priceType: nval.priceType,
      contractAmount: nval.contractAmount,
      taxExcludedAmount: nval.taxExcludedAmount,
      taxIncludedAmount: nval.taxIncludedAmount,
      taxAmount: nval.taxAmount,
      beforeCumulationTaxIncludedAmount: nval.beforeCumulationTaxIncludedAmount,
      beforeCumulationTaxExcludedAmount: nval.beforeCumulationTaxExcludedAmount,
      beforeCumulationTaxAmount: nval.beforeCumulationTaxAmount,
    };
    formData.value = form;
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };

    getContractOptionsBySupplierId();
  },
);

const localEditable = computed(() => {
  return (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    actionPermissions.apUpdate
  );
});

watch(
  () => formData,
  (nval, oval: any) => {
    oldFormData.value = {
      id: oval.id,
      orgName: oval.orgName,
      supplierId: oval.supplierId,
      contractId: oval.contractId,
      code: oval.code,
      supplierName: oval.supplierName,
      contractName: oval.contractName,
      settlementDate: oval.settlementDate,
      taxRate: oval.taxRate,
      priceType: oval.priceType,
      contractAmount: oval.contractAmount,
      taxExcludedAmount: oval.taxExcludedAmount,
      taxIncludedAmount: oval.taxIncludedAmount,
      taxAmount: oval.taxAmount,
      beforeCumulationTaxIncludedAmount: oval.beforeCumulationTaxIncludedAmount,
      beforeCumulationTaxExcludedAmount: oval.beforeCumulationTaxExcludedAmount,
      beforeCumulationTaxAmount: oval.beforeCumulationTaxAmount,
    };
  },
);

// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);
const addMaterialinfoData = ref();

// 内置节点数据
const addBtnBlockItem = {
  id: null,
  name: null,
  internal: true, // 代表是内置节点
};

const tableRef = ref();
const currentItem = ref({
  id: '',
  materialName: '',
  materialSpec: '',
});
const unitOptions = ref([]);

const columns = [
  {
    field: 'seq',
    title: '',
    width: '50',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '140',
    slots: {
      default: 'materialName',
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '100',
  },
  {
    field: 'quantity',
    title: '收料数量',
    minWidth: '100',
    slots: {
      default: 'quantity',
    },
  },
  {
    field: 'price',
    title: '收料单价',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'amount',
    title: '收料金额(元)',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'settlementQuantity',
    title: '结算数量',
    minWidth: '100',
  },
  {
    field: 'settlementPrice',
    title: '结算单价',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'settlementAmount',
    title: '结算金额(元)',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: '60',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  cellClassName: ({ row, column }: any) => {
    return !column.editRender ||
      column.editRender.enabled === false ||
      row.internal
      ? 'bg-gray-100'
      : '';
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',

            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',

            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, rowIndex, row }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            item.disabled = rowIndex === tableOptions.data.length - 2;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = rowIndex === 0;
            break;
          }
        }
        if (!localEditable.value) {
          item.disabled = true;
        }
      });
      return !!row.id;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前没有修改权限');
        return;
      }
      // 如果是内置数据则不可修改
      if (row.internal) {
        return false;
      }
      // 版本数据已启用无法进行编辑
      if (!localEditable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    {
      id: null,
      materialName: '合计',
      internal: true,

      amount: 0,
      settlementAmount: 0,
    },
  ],
});

// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({
    menu,
    rowIndex,
    row,
  }: {
    menu: any;
    row: any;
    rowIndex: any;
  }) {
    if (menu.code === 'MOVE_DOWN') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex + 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
    if (menu.code === 'MOVE_UP') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex - 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      refreshData();
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    const data = {
      id: row.id,
      settlementId: localInfoData.value.id,
      remark: row.remark,
    };
    const res = await updateSettlementDetail(data);
    if (res) {
      refreshData();
    }
  },
};

watch(
  () => currentItem.value.id,
  async (nval) => {
    await getTraceDetailList();
  },
);

async function getTraceDetailList() {
  const settlementDetailId = currentItem.value.id;
  const settlementId = localInfoData.value.id;
  const ret = await getMaterialReceivingReturnDetails({
    settlementId,
    settlementDetailId,
  });
  isTraceDetailTree.value = false;
  traceDetailList.value = ret.map((item: any) => {
    if (item.parentId) {
      isTraceDetailTree.value = true;
    }
    item.name = item.code;
    return item;
  });
}

// 刷新数据
async function refreshData() {
  const { id } = localInfoData.value;
  const [refreshItem] = await getMaterialSettlementList({ id });
  refreshItem.orgName = refreshItem.projectName;
  formData.value = refreshItem;
  await getList();
  await getTraceList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }

  if (localInfoData.value.submitStatus === SubmitStatus.PENDING) {
    // 需要提交数据了
    const isPassForm = checkForm();
    if (!isPassForm) {
      return;
    }

    if (tableOptions.data.length <= 1) {
      ElMessage.warning('请添加材料');
      return;
    }
  }

  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const { id } = formData.value;

  const data = {
    submitStatus, // 修改提交状态为已提交
  };

  const res = await changeSubmitStatus(id, data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

// 确认修改
async function insureSave() {
  const params = {
    supplierId: formData.value.supplierId,
    supplierName: formData.value.supplierName,
    contractId: formData.value.contractId,
    contractName: formData.value.contractName,
    settlementDate: formData.value.settlementDate,
  };

  try {
    await editMaterialSettlement(formData.value.id, params);
  } catch {
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;
      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;
      formData.value.settlementDate = oldFormData.value.settlementDate;

      getContractOptionsBySupplierId();
    });
  }
}

// 供应商改变
async function supplierChange() {
  // 清单中有数据则不可修改
  if (tableOptions.data.length > 2) {
    // 还原为旧值
    nextTick(() => {
      formData.value.supplierId = oldFormData.value.supplierId;
      formData.value.supplierName = oldFormData.value.supplierName;
    });

    ElMessage.warning('请先删除材料数据后再修改');

    return;
  }

  if (formData.value.supplierId) {
    // 更改合同选项范围
    getContractOptionsBySupplierId();
    formData.value.contractId = '';
    formData.value.contractName = '';
    if (contractOptions.value.length === 1) {
      formData.value.contractId = contractOptions.value[0].value;
      formData.value.contractName = contractOptions.value[0].label;
    }
  } else {
    resetData();
  }

  await insureSave();
}

function getContractOptionsBySupplierId() {
  if (formData.value.supplierId) {
    const data = supplierOptions.value.find(
      (item: any) => item.supplierId === formData.value.supplierId,
    );
    formData.value.supplierName = data.label;

    // 筛选出合同的数据
    contractOptions.value = data?.contracts || [];
  }
}

// 合同改变
async function contractChange() {
  // 清单中有树据则不可修改
  if (tableOptions.data.length > 1) {
    // 还原为旧值
    nextTick(() => {
      formData.value.contractId = oldFormData.value.contractId;
      formData.value.contractName = oldFormData.value.contractName;
    });
    ElMessage.warning('请先删除材料数据后再修改');
    return;
  }

  if (formData.value.contractId) {
    // 同时给名称赋值
    const data = contractOptions.value.find(
      (item: any) => item.value === formData.value.contractId,
    );
    formData.value.contractName = data.label;

    if (!formData.value.supplierId) {
      formData.value.supplierId = data.supplierId;
      formData.value.supplierName = data.supplierName;

      // 更改合同选项范围
      getContractOptionsBySupplierId();
    }
  } else {
    resetData();
  }

  await insureSave();
}

// 结算日期改变
async function entryDateChange() {
  if (tableOptions.data.length > 1) {
    // 还原为旧值
    nextTick(() => {
      formData.value.settlementDate = oldFormData.value.settlementDate;
    });
    ElMessage.warning('请先删除材料数据后再修改');
    return;
  }
  await insureSave();
}

async function resetData() {
  supplierOptions.value = entryCheckAllSupplierData;
  contractOptions.value = entryCheckAllContractData;

  formData.value.supplierId = '';
  formData.value.supplierName = '';
  formData.value.contractId = '';
  formData.value.contractName = '';
}

// 计算合计的含税单价 和 单价
function countTotalPrice() {
  const totalItem = tableOptions.footerData[0];
  let amount = '';
  let settlementAmount = '';
  const $grid = tableRef.value;
  if ($grid) {
    const targetData = tableOptions.data;
    targetData
      .filter((v: any) => !v.internal)
      .forEach((item: any) => {
        if (item.amount) {
          amount = add(amount, item.amount).toNumber().toFixed(2);
        }
        if (item.settlementAmount) {
          settlementAmount = add(settlementAmount, item.settlementAmount)
            .toNumber()
            .toFixed(2);
        }
      });
    totalItem.amount = amount;
    totalItem.settlementAmount = settlementAmount;
  }
}

// 弹窗打开回调
async function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);

// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const tableData = await getSettlementDetails({ settlementId: id });

  tableOptions.data =
    tableData.length === 0
      ? [addBtnBlockItem]
      : [...tableData, addBtnBlockItem];

  countTotalPrice();
}

async function getTraceList() {
  if (localInfoData.value.contractId && localInfoData.value.supplierId) {
    const res = await getReceivingReturnList({
      settlementId: localInfoData.value.id,
      contractId: localInfoData.value.contractId,
      supplierId: localInfoData.value.supplierId,
    });
    const selectedBills = <any>[];
    res.forEach((v) => {
      v.name = v.code;
      if (v.isSelected) {
        selectedInfo.selectedNum += 1;
        selectedBills.push(v);
      } else {
        selectedInfo.noSelectedNum += 1;
        if (v.auditStatus === AuditStatus.APPROVED) {
          selectedInfo.noSelectedApprovedNum += 1;
        } else {
          selectedInfo.noSelectedOtherNum += 1;
        }
      }
    });
    traceList.value = selectedBills;
  }
}

// 点击穿梭框
async function transferDataClick() {
  if (!checkForm()) {
    return;
  }
  curTab.value = SettlementType.PURCHASE;

  addMaterialinfoData.value = {
    settlementId: formData.value.id,
    contractId: formData.value.contractId,
    supplierId: formData.value.supplierId,
  };

  addOrEditMaterialVisible.value = true;
}

function checkForm() {
  if (!formData.value.supplierId) {
    ElMessage.warning('请选择供应商名称');
    return false;
  }
  if (!formData.value.contractId) {
    ElMessage.warning('请选择合同名称');
    return false;
  }

  return true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}

// 获取供应商名称
async function getSupplierContractList() {
  const rets = await getSupplierAndContractList();
  const supplierMap: Record<string, any> = {};

  contractOptions.value = [];
  rets.forEach((item: any) => {
    const contractOption = {
      supplierId: item.supplierId,
      supplierName: item.supplierName,
      contractId: item.contractId,

      label: item.contractName,
      value: item.contractId,
    };
    contractOptions.value.push(contractOption);
    if (supplierMap[item.supplierId]) {
      supplierMap[item.supplierId].contracts.push(contractOption);
    } else {
      supplierMap[item.supplierId] = {
        supplierId: item.supplierId,
        contracts: [contractOption],

        label: item.supplierName,
        value: item.supplierId,
      };
    }
  });

  supplierOptions.value = Object.values(supplierMap);
  entryCheckAllSupplierData = supplierOptions.value;
  entryCheckAllContractData = contractOptions.value;
}

// 新增附件
async function addAnnex(data: any) {
  const _data = {
    settlementId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addMaterialReceivingAttachment(_data);

  if (res) {
    ElMessage.success('添加成功');
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;

  const res = await delAttachment(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}
// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}

// 获取数据溯源数据
async function traceActualQuanity(row: any) {
  extrasOpen.value = true;
  extrasTab.value = 'TRACEDETAIL';
}

// 删除数据溯源
async function delTraceBack({ row }: any) {
  const id = row.id;
  const res = await delRecord({
    settlementId: localInfoData.value.id,
    receivingReturnIds: [id],
  });
  if (res) {
    ElMessage.success('删除成功');

    traceList.value = traceList.value
      .filter((v: any) => v.id !== id)
      .map((item: any) => {
        item.name = item.code;
        return item;
      });
    refreshData();
  }
}

const pageReturnState = usePageReturnState();
const route = useRoute();
const router = useRouter();
// 数据溯源跳转
async function jumpTraceRecord({ row }: any) {
  const routeName = route.name as string;
  pageReturnState.setReturnState({
    fromPage: routeName,
    recordId: localInfoData.value?.id ?? '',
    isDialogOpen: true,
  });
  switch (row.settlementBillType) {
    case 'INCOMING_INSPECTION': {
      router.push({
        name: 'MenuMaterialEntryCheck',
        query: { code: row.code },
      });

      break;
    }
    case 'RECEIVING': {
      router.push({
        name: 'MenuMaterialReceivingForm',
        query: { code: row.code },
      });

      break;
    }
    case 'RETURN_SALES': {
      router.push({
        name: 'MenuMaterialReturnOrderForm',
        query: { code: row.code },
      });

      break;
    }
    // No default
  }
}

async function init() {}

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

onBeforeMount(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}

.bold-label {
  font-weight: bold;
}
::v-deep(.el-form-item__label) {
  padding: 0 2px 0 0;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 16px;
}
</style>
