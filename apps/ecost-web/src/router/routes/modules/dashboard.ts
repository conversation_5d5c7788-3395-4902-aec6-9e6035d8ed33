import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // 已改为服务端返回动态路由
  // {
  //   meta: {
  //     icon: 'lucide:layout-dashboard',
  //     order: -1,
  //     title: '概览',
  //   },
  //   name: 'Dashboard',
  //   path: '/dashboard',
  //   children: [
  //     {
  //       name: 'workspace',
  //       path: '/workspace',
  //       component: () => import('#/views/dashboard/workspace/index.vue'),
  //       meta: {
  //         icon: 'lucide:area-chart',
  //         title: '工作台',
  //       },
  //     },
  //     {
  //       name: 'MenuSystemEntry',
  //       path: '/menu-system-entry',
  //       meta: {
  //         icon: 'lucide:area-chart',
  //         title: '系统入口',
  //       },
  //       children: [
  //         {
  //           name: 'AppEcost',
  //           path: '/dashboard/menu-system-entry/ecost',
  //           component: () => {},
  //           meta: {
  //             icon: 'carbon:workspace',
  //             title: '项目成本控制系统',
  //             isSystem: true,
  //             productCode: 'ecost',
  //           },
  //         },
  //         {
  //           name: 'AppErebar',
  //           path: '/dashboard/menu-system-entry/erebar',
  //           component: () => {},
  //           meta: {
  //             icon: 'carbon:workspace',
  //             title: '金箍棒',
  //             isSystem: true,
  //             productCode: 'ecost',
  //           },
  //         },
  //         {
  //           name: 'AppElink',
  //           path: '/dashboard/menu-system-entry/elink',
  //           component: () => {},
  //           meta: {
  //             icon: 'carbon:workspace',
  //             title: '易联e通',
  //             isSystem: true,
  //             productCode: 'ecost',
  //           },
  //         },
  //         {
  //           name: 'AppAdminSystemCenter',
  //           path: '/dashboard/menu-system-entry/admin-system-center',
  //           component: () => {},
  //           meta: {
  //             icon: 'carbon:workspace',
  //             title: '系统中心(Admin)',
  //             isSystem: true,
  //             productCode: 'admin-system-center',
  //           },
  //         },
  //       ],
  //     },
  //   ],
  // },
];

export default routes;
