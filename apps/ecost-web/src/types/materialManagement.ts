// 提交状态
export const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;
export type SubmitStatusType = (typeof SubmitStatus)[keyof typeof SubmitStatus];
export const submitStatusOption = [
  { label: '未提交', value: SubmitStatus.PENDING },
  { label: '已提交', value: SubmitStatus.SUBMITTED },
];
export function getSubmitStatusLabel(status: SubmitStatusType) {
  const map = {
    [SubmitStatus.PENDING]: '未提交',
    [SubmitStatus.SUBMITTED]: '已提交',
  };
  return map[status] || '';
}

// 审核状态
export const AuditStatus = {
  PENDING: 'PENDING', // 待审核
  AUDITING: 'AUDITING', // 审批中
  APPROVED: 'APPROVED', // 审核通过
  REJECTED: 'REJECTED', // 审核拒绝
} as const;
export type AuditStatusType = (typeof AuditStatus)[keyof typeof AuditStatus];
export function getAuditStatusLabel(status: AuditStatusType) {
  const map = {
    [AuditStatus.PENDING]: '未审批',
    [AuditStatus.AUDITING]: '审批中',
    [AuditStatus.APPROVED]: '已审批',
    [AuditStatus.REJECTED]: '被退回',
  };
  return map[status] || '';
}
export const auditStatusOption = [
  { label: '未审批', value: AuditStatus.PENDING },
  { label: '审批中', value: AuditStatus.AUDITING },
  { label: '已审批', value: AuditStatus.APPROVED },
  { label: '被退回', value: AuditStatus.REJECTED },
];

// 收料状态
export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED', // 未收料
  RECEIVED: 'RECEIVED', // 已收料
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED', // 部分收料
} as const;
export type MaterialReceiptStatusType =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export function getMaterialReceiptStatusLabel(
  status: MaterialReceiptStatusType,
) {
  const map = {
    [MaterialReceiptStatus.UN_RECEIVED]: '未收料',
    [MaterialReceiptStatus.RECEIVED]: '已收料',
    [MaterialReceiptStatus.PARTIAL_RECEIVED]: '部分收料',
  };
  return map[status] || '';
}
export const materialReceiptStatusOption = [
  { label: '未收料', value: MaterialReceiptStatus.UN_RECEIVED },
  { label: '已收料', value: MaterialReceiptStatus.RECEIVED },
  { label: '部分收料', value: MaterialReceiptStatus.PARTIAL_RECEIVED },
];

// 结算状态
export const MaterialSettlementStatus = {
  UN_SETTLED: 'UN_SETTLED', // 未收料
  SETTLED: 'SETTLED', // 已收料
} as const;
export type MaterialSettlementStatusType =
  (typeof MaterialSettlementStatus)[keyof typeof MaterialSettlementStatus];

export function getMaterialSettlementStatusLabel(
  status: MaterialSettlementStatusType,
) {
  const map = {
    [MaterialSettlementStatus.UN_SETTLED]: '未结算',
    [MaterialSettlementStatus.SETTLED]: '已结算',
  };
  return map[status] || '';
}
export const materialSettlementStatusOption = [
  { label: '未结算', value: MaterialSettlementStatus.UN_SETTLED },
  { label: '已结算', value: MaterialSettlementStatus.SETTLED },
];

// 采购类型
export const PurchaseType = {
  SELF_PURCHASE: 'SELF_PURCHASE', // 自采
  CENTRALIZED_PURCHASE: 'CENTRALIZED_PURCHASE', // 集采
  PARTY_A_DIRECTED: 'PARTY_A_DIRECTED', // 甲指
  PARTY_A_SUPPLIED: 'PARTY_A_SUPPLIED', // 甲供
  TRANSFER_IN: 'TRANSFER_IN', // 调拨
} as const;
export type PurchaseTypeEnum = (typeof PurchaseType)[keyof typeof PurchaseType];

export function getPurchaseTypeLabel(status: PurchaseTypeEnum) {
  const map = {
    [PurchaseType.SELF_PURCHASE]: '自采',
    [PurchaseType.CENTRALIZED_PURCHASE]: '集采',
    [PurchaseType.PARTY_A_DIRECTED]: '甲指',
    [PurchaseType.PARTY_A_SUPPLIED]: '甲供',
    [PurchaseType.TRANSFER_IN]: '调入',
  };
  return map[status] || status;
}
export const purchaseTypeLabelOption = [
  { label: '自采', value: PurchaseType.SELF_PURCHASE },
  { label: '集采', value: PurchaseType.CENTRALIZED_PURCHASE },
  { label: '甲指', value: PurchaseType.PARTY_A_DIRECTED },
  { label: '甲供', value: PurchaseType.PARTY_A_SUPPLIED },
  { label: '调入', value: PurchaseType.TRANSFER_IN },
];

// 核算类型
export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL', // 消耗材料
  CONCRETE: 'CONCRETE', // 商品混凝土
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL', // 周转材料
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES', // 固定资产/低值易耗品
};
export type MaterialEnum = (typeof MaterialType)[keyof typeof MaterialType];
export function getMaterialTypeLabel(status: MaterialEnum) {
  const map = {
    [MaterialType.CONSUME_MATERIAL]: '消耗材料',
    [MaterialType.CONCRETE]: '商品混凝土',
    [MaterialType.TURNOVERME_MATERIAL]: '周转材料',
    [MaterialType.FIXEDASSETSL_CONSUMABLES]: '固定资产/低值易耗品',
  };
  return map[status] || '';
}
export const materialTypeLabelOption = [
  { label: '消耗材料', value: MaterialType.CONSUME_MATERIAL },
  { label: '商品混凝土', value: MaterialType.CONCRETE },
  { label: '周转材料', value: MaterialType.TURNOVERME_MATERIAL },
  {
    label: '固定资产/低值易耗品',
    value: MaterialType.FIXEDASSETSL_CONSUMABLES,
  },
];

// 结算类型
export const SettlementType = {
  PURCHASE: 'PURCHASE', // 采购
  ALLOCATION_FROM: 'ALLOCATION_FROM', // 调拨
  CONCRETE: 'CONCRETE', // 商品混凝土
  RENTAL_TURNOVER: 'RENTAL_TURNOVER', // 租赁周转材料
};

export const settlementTypeLabelOption = [
  { label: '采购', value: SettlementType.PURCHASE },
  { label: '调拨', value: SettlementType.ALLOCATION_FROM },
  { label: '商品混凝土', value: SettlementType.CONCRETE },
  { label: '租赁周转材料', value: SettlementType.RENTAL_TURNOVER },
];

export type SettlementTypeEnum =
  (typeof SettlementType)[keyof typeof SettlementType];

export function getSettlementTypeLabel(status: SettlementTypeEnum) {
  const map = {
    [SettlementType.PURCHASE]: '采购',
    [SettlementType.ALLOCATION_FROM]: '调拨',
    [SettlementType.CONCRETE]: '商品混凝土',
    [SettlementType.RENTAL_TURNOVER]: '租赁周转材料',
  };
  return map[status] || '';
}

// 履约状态
export const FulfillmentStatus = {
  COMPLETED: 'COMPLETED', // 已结束
  IN_PROGRESS: 'IN_PROGRESS', // 履约中
  NOT_STARTED: 'NOT_STARTED', // 未开始
} as const;
type FulfillmentStatusType =
  (typeof FulfillmentStatus)[keyof typeof FulfillmentStatus];

export function getFulfillmentStatusLabel(status: FulfillmentStatusType) {
  const map = {
    [FulfillmentStatus.NOT_STARTED]: '未开始',
    [FulfillmentStatus.IN_PROGRESS]: '履约中',
    [FulfillmentStatus.COMPLETED]: '已结束',
  };
  return map[status] || '未知状态';
}
export const fulfillmentStatusOptions = [
  { label: '未开始', value: FulfillmentStatus.NOT_STARTED },
  { label: '履约中', value: FulfillmentStatus.IN_PROGRESS },
  { label: '已结束', value: FulfillmentStatus.COMPLETED },
];

// 合同类型
export const ContractTemplateType = {
  GENERAL: 'GENERAL', // 通用
  SUBPACKAGE_LABOUR_SERVICE: 'SUBPACKAGE_LABOUR_SERVICE', // 分包-劳务
  SUBPACKAGE_LABOUR_SPECIALTY: 'SUBPACKAGE_LABOUR_SPECIALTY', // 分包-专业

  MATERIALS_PURCHASING: 'MATERIALS_PURCHASING', // 材料-物资采购
  MATERIALS_COMMERCIAL_CONCRETE: 'MATERIALS_COMMERCIAL_CONCRETE', // 材料-商品混凝土
  MATERIALS_LEASING_TURNOVER: 'MATERIALS_LEASING_TURNOVER', // 材料-租赁周转材料

  MACHINERY: 'MACHINERY', // 机械
  OTHERS: 'OTHERS', // 其他
} as const;
type ContractTemplateType =
  (typeof ContractTemplateType)[keyof typeof ContractTemplateType];

export function getContractTemplateTypeLabel(status: ContractTemplateType) {
  const map = {
    [ContractTemplateType.GENERAL]: '通用',
    [ContractTemplateType.SUBPACKAGE_LABOUR_SERVICE]: '分包-劳务',
    [ContractTemplateType.SUBPACKAGE_LABOUR_SPECIALTY]: '分包-专业',

    [ContractTemplateType.MATERIALS_PURCHASING]: '材料-物资采购',
    [ContractTemplateType.MATERIALS_COMMERCIAL_CONCRETE]: '材料-商品混凝土',
    [ContractTemplateType.MATERIALS_LEASING_TURNOVER]: '材料-租赁周转材料',

    [ContractTemplateType.MACHINERY]: '机械',
    [ContractTemplateType.OTHERS]: '其他',
  };
  return map[status] || '未知状态';
}
export const contractTemplateTypeOptions = [
  { label: '通用', value: ContractTemplateType.GENERAL },
  { label: '分包-劳务', value: ContractTemplateType.SUBPACKAGE_LABOUR_SERVICE },
  {
    label: '分包-专业',
    value: ContractTemplateType.SUBPACKAGE_LABOUR_SPECIALTY,
  },
  { label: '材料-物资采购', value: ContractTemplateType.MATERIALS_PURCHASING },
  {
    label: '材料-商品混凝土',
    value: ContractTemplateType.MATERIALS_COMMERCIAL_CONCRETE,
  },
  {
    label: '材料-租赁周转材料',
    value: ContractTemplateType.MATERIALS_LEASING_TURNOVER,
  },
  { label: '机械', value: ContractTemplateType.MACHINERY },
  { label: '其他', value: ContractTemplateType.OTHERS },
];
