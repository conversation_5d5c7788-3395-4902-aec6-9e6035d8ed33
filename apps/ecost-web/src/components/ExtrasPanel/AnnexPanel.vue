<template>
  <div class="h-full">
    <div class="header flex items-center justify-between pb-4">
      <div class="title text-[16px] text-blue-500">附件</div>
      <div @click="panelClose" class="cursor-pointer text-[18px]">
        <ElIcon><Close class="text-black" /></ElIcon>
      </div>
    </div>
    <div class="content h-[calc(100%_-_30px)] overflow-scroll">
      <BaseUpload
        ref="baseUploadRef"
        v-bind="attrs"
        v-model:file-list="localFileList"
        :editable="localEditable"
        list-type="picture"
        @success="(data) => emit('success', data)"
        @remove="(file) => emit('remove', file)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, useAttrs, watch } from 'vue';

import { Close } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';

import BaseUpload from '#/components/BaseUpload/BaseUpload.vue';

const props = withDefaults(
  defineProps<{
    editable: boolean;
    fileList?: any;
  }>(),
  {
    fileList: [], // 文件列表数据
    editable: true,
  },
);

const emit = defineEmits<{
  (e: 'update:fileList', fileList: any): void;
  (e: 'success', data: any): void;
  (e: 'remove', file: any): void;
  (e: 'close'): void;
}>();
const baseUploadRef = ref();
const attrs = useAttrs();
const localFileList = ref(props.fileList);
watch(
  () => props.fileList,
  (val) => {
    localFileList.value = [...val];
  },
  {
    immediate: true,
  },
);
const localEditable = ref(props.editable);
watch(
  () => props.editable,
  (val) => {
    localEditable.value = val;
  },
  {
    immediate: true,
  },
);
function panelClose() {
  emit('close');
}
</script>
