<template>
  <div
    class="z-100 absolute top-0 h-full w-[400px] rounded-[12px] bg-[#eee] p-4 text-[14px]"
    :style="{
      right: localOpenStatus ? '0' : '-400px',
      transition: 'right 0.3s cubic-bezier(0.4,0,0.2,1)',
    }"
  >
    <div class="btnGroup absolute left-[-22px] top-[64px]">
      <div
        v-for="v in localOptions"
        :key="v.key"
        :class="`flex min-h-[56px] w-[22px] cursor-pointer items-center justify-center border border-gray-400 p-[4px] text-center text-[10px] text-white ${v.color}`"
        @click="changeTab(v.key)"
      >
        {{ v.name }}
      </div>
    </div>

    <RecordPanel
      v-show="localCurTab === 'CHANGERECORD'"
      :record-list="recordList"
      :filed-key-name="filedKeyName"
      :editable="localEditable"
      @close="changeLogStatus('close')"
    />

    <AnnexPanel
      v-show="localCurTab === 'ANNEX'"
      :file-list="fileList"
      :editable="localEditable"
      @close="changeLogStatus('close')"
      @success="(e) => emit('successAnnex', e)"
      @remove="(e) => emit('delAnnex', e)"
    />

    <TraceRecordPanel
      v-show="localCurTab === 'TRACERECORD'"
      :data="traceList"
      :editable="localEditable"
      :columns="traceColumns"
      :info-data="localInfoData"
      :is-tree="isTraceRecordTree"
      @jump="(e) => emit('jumpTraceRecord', e)"
      @close="changeLogStatus('close')"
      @remove="(e) => emit('delTraceRecord', e)"
    >
      <template #traceRecordHeader>
        <slot name="traceRecordHeader"></slot>
      </template>
    </TraceRecordPanel>

    <TraceDetailPanel
      v-show="localCurTab === 'TRACEDETAIL'"
      :data="traceDetailList"
      :editable="localEditable"
      :columns="traceDetailColumns"
      :info-data="localInfoData"
      :is-tree="isTraceDetailTree"
      @jump="(e) => emit('jumpTraceDetail', e)"
      @remove="(e) => emit('delTraceDetail', e)"
      @close="changeLogStatus('close')"
    >
      <template #traceDetailHeader>
        <slot name="traceDetailHeader"></slot>
      </template>
    </TraceDetailPanel>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

import AnnexPanel from './AnnexPanel.vue';
import RecordPanel from './RecordPanel.vue';
import TraceDetailPanel from './TraceDetailPanel.vue';
import TraceRecordPanel from './TraceRecordPanel.vue';

// 传入的数据类型
interface RecordListType {
  id: string; // id
  createAt: string; // 创建时间
  createByName: string; // 创建人
  versionNumber: string; // 版本号 用于按照其数据进行组合
  fieldKey: string; // 字段key
  newValue: string; // 新值
  oldValue: string; // 旧值
}
type VisibleOptionEnum =
  | 'ANNEX'
  | 'CHANGERECORD'
  | 'TRACEDETAIL'
  | 'TRACERECORD';
const props = withDefaults(
  defineProps<{
    curTab?: string; // 打开的tab
    editable?: boolean; // 是否可编辑

    fileList?: any; // 附件列表
    infoData?: any; // 当前页面的基本信息，用于回跳
    isTraceDetailTree?: boolean; // 数据明细是否是tree数据
    isTraceRecordTree?: boolean; // 数据溯源是否是tree数据
    openStatus?: boolean; // 打开状态
    recordFiledKey?: any; // 变更记录【key:名称】映射
    recordList?: RecordListType[]; // 变更记录
    traceColumns?: any; // 溯源列
    traceDetailColumns?: any; // 追溯明细列
    traceDetailList?: any; // 追溯明细

    traceList?: any; // 溯源数据
    visibleOption?: VisibleOptionEnum[]; // 默认展示的项
  }>(),
  {
    infoData: () => {},
    isTraceRecordTree: false,
    isTraceDetailTree: false,
    openStatus: false,
    curTab: 'ANNEX',
    editable: false,
    fileList: [],
    recordFiledKey: {},
    recordList: () => [] as RecordListType[], // 变更记录数据
    traceList: [], // 数据溯源
    traceDetailList: [],
    traceColumns: [],
    traceDetailColumns: [],
    visibleOption: () => ['ANNEX'], // 默认展示的项
  },
);

const emit = defineEmits<{
  (e: 'update:curTab', curTab: any): void;
  (e: 'update:openStatus', openStatus: boolean): void;
  (e: 'successAnnex', payload: any): void;
  (e: 'delAnnex', payload: any): void;
  (e: 'delTraceRecord', payload: any): void;
  (e: 'delTraceDetail', payload: any): void;
  (e: 'jumpTraceRecord', payload: any): void; // 数据明细跳转
  (e: 'jumpTraceDetail', payload: any): void; // 数据溯源跳转
}>();
// 审核状态
const visibleOptionEnum = {
  ANNEX: 'ANNEX', // 附件
  CHANGERECORD: 'CHANGERECORD', // 变更记录
  TRACEDETAIL: 'TRACEDETAIL', // 追溯明细
  TRACERECORD: 'TRACERECORD', // 数据溯源
} as const;

const localEditable = ref(props.editable);
watch(
  () => props.editable,
  (nval) => {
    localEditable.value = nval;
  },
  {
    immediate: true,
  },
);
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
  {
    immediate: true,
  },
);

const filedKeyName = ref({
  companyName: '公司名称',
  unifiedSocialCreditCode: '统一社会信用代码',
  registeredAddress: '注册地址',
  companyLocation: '企业所在地',
  taxpayerType: '纳税人身份',
  businessPhone: '业务电话',
  bankName: '开户银行',
  bankAddress: '开户地址',
  bankAccount: '开户账号',
});
watch(
  () => props.recordFiledKey,
  (nval) => {
    filedKeyName.value = nval;
  },
  {
    immediate: true,
  },
);

const options = [
  { key: visibleOptionEnum.ANNEX, name: '附件', color: 'bg-blue-500' },
  {
    key: visibleOptionEnum.CHANGERECORD,
    name: '变更记录',
    color: 'bg-green-500',
  },
  {
    key: visibleOptionEnum.TRACERECORD,
    name: '数据溯源',
    color: 'bg-green-500',
  },
  {
    key: visibleOptionEnum.TRACEDETAIL,
    name: '追溯明细',
    color: 'bg-green-500',
  },
];

const localOptions = ref(options);
watch(
  () => props.visibleOption,
  (nval) => {
    localOptions.value = options.filter((v: any) => nval.includes(v.key));
  },
  { immediate: true },
);

const localCurTab = ref<any>(props.visibleOption[0]);
watch(
  () => props.curTab,
  (nval) => {
    localCurTab.value = nval;
  },
  { immediate: true },
);

const localOpenStatus = ref(false); // 是否打开

watch(
  () => props.openStatus,
  (nval) => {
    localOpenStatus.value = nval;
  },
);

function changeLogStatus(type?: string) {
  if (type === 'close') {
    localOpenStatus.value = false;
    emit('update:openStatus', false);
  }
}
function changeTab(tab: string) {
  if (localCurTab.value === tab && localOpenStatus.value) {
    localOpenStatus.value = false;
    emit('update:openStatus', false);
  } else {
    localCurTab.value = tab;
    localOpenStatus.value = true;
    emit('update:openStatus', true);
  }

  localCurTab.value = tab;

  emit('update:curTab', tab);
}
</script>
