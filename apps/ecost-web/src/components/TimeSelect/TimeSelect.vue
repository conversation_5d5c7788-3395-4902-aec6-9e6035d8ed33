<template>
  <div
    class="h-full items-center overflow-hidden transition-all duration-300 ease-in-out"
    :style="{ width: isCollapsed ? '30px' : '200px' }"
  >
    <div v-show="!isCollapsed" class="flex h-full flex-col">
      <div class="mb-2 flex h-[40px] items-center">
        <ElDatePicker
          v-model="time"
          type="date"
          placeholder="请选择日期"
          value-format="YYYY/MM/DD"
          size="default"
          :disabled-date="disabledDate"
          @change="changeTime"
          @clear="clear"
        />
        <div
          class="item-center flex h-full w-[40px] items-center justify-center"
        >
          <ElIcon
            class="cursor-pointer hover:text-sky-500"
            size="18"
            v-if="isCollapsed"
            @click="isCollapsed = false"
          >
            <DArrowRight />
          </ElIcon>
          <ElIcon
            class="cursor-pointer hover:text-sky-500"
            size="18"
            v-if="!isCollapsed"
            @click="isCollapsed = true"
          >
            <DArrowLeft />
          </ElIcon>
        </div>
      </div>
      <div class="time-select-table h-[calc(100%-40px)]">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #top> </template>
          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>
          <template #name="{ row }">
            <div class="font-bold">
              {{ `${row.name} (${row.count})` }}
            </div>
          </template>
        </VxeGrid>
      </div>
    </div>
    <div
      v-show="isCollapsed"
      class="flex h-[30px] w-[30px] cursor-pointer items-center justify-center"
      @click="isCollapsed = false"
    >
      <ElIcon size="18" class="hover:text-sky-500">
        <DArrowRight />
      </ElIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRef, watchEffect } from 'vue';

import { DArrowLeft, DArrowRight } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElDatePicker, ElIcon } from 'element-plus';

import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

interface TimeDataType {
  count: number;
  id: string;
  month: number;
  parentId: null | string;
  year: number;
}

const props = withDefaults(
  defineProps<{
    timeData: TimeDataType[];
  }>(),
  {
    timeData: () => [],
  },
);

const emit = defineEmits<{
  (e: 'select', row: TimeDataType): void;
}>();

const isCollapsed = ref(false); // 是否收起

const staticItem = {
  id: '',
  name: '全部',
  count: 0,
  disabled: true,
  editable: true,
};
const columns = [
  {
    field: 'name',
    title: '时间',
    treeNode: true,
    slots: {
      default: 'name',
    },
  },
];
const disabledDateArr = ref<any>([]);
const disabledDate = (time: Date) => {
  const curDate = dayjs(time).format('YYYY/MM/DD');
  return !disabledDateArr.value.includes(curDate);
};

const time = ref();
const tableRef = ref();
const currentItem = ref({
  id: '',
  name: '全部',
  count: 0,
  year: null,
  month: null,
  day: null,

  internal: true, // 内置节点 用于处理内置节点的逻辑
  disabled: true,
  editable: false,
});
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  height: '100%',
  loading: false,
  columns,
  data: [],
});

const tableEvents = {
  cellClick({ row }: any) {
    currentItem.value = row;
    const day = row.day;
    const year = row.year;
    const month = row.month;
    if (row.day && row.year && row.month) {
      const timeText = `${year}/${month}/${day}`;
      time.value = dayjs(timeText).format('YYYY/MM/DD');
    } else {
      time.value = '';
    }

    emit('select', row);
  },
};

// 传入的timeData数据
const rawTimeData = toRef(props, 'timeData');
watchEffect(() => {
  updateTable(rawTimeData.value);
});

async function updateTable(timeData: any) {
  const res = timeData;

  let totalCount = 0;
  const resData = res.map((item: any) => {
    if (item.parentId) {
      totalCount += item.count;
    }

    let name;
    if (item.parentId) {
      const time = `${item.year}/${item.month}/${item.day}`;
      name = dayjs(time).format('DD日');
    } else {
      const time = `${item.year}/${item.month}`;
      name = dayjs(time).format('YYYY年M月');
    }

    return {
      ...item,
      name,
    };
  });

  disabledDateArr.value = res
    .filter((v: any) => v.parentId)
    .map((item: any) => {
      const time = `${item.year}/${item.month}/${item.day}`;
      return dayjs(time).format('YYYY/MM/DD');
    });

  staticItem.count = totalCount;
  tableOptions.data = [staticItem, ...resData];

  // 防止dom还没有拿到就调用
  if (tableRef.value) {
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value, true);
  }
}

const clear = () => {
  const rowData = tableOptions.data[0];
  tableEvents.cellClick({ row: rowData });
  setCurrentRow(tableOptions.data, tableRef.value, rowData, true);
};

const changeTime = async () => {
  const rowData = tableOptions.data.find((item: any) => {
    return item.day && item.year && item.month
      ? dayjs(`${item.year}/${item.month}/${item.day}`).format('YYYY/MM/DD') ===
          time.value
      : false;
  });
  if (rowData) {
    tableEvents.cellClick({ row: rowData });
    setCurrentRow(tableOptions.data, tableRef.value, rowData, true);
  }
};

defineExpose({
  clear,
});
</script>
