# 📅 TimeSelect 组件使用说明

## 📋 功能概述

`TimeSelect` 是一个用于选择年月日时间节点的侧边栏组件，具备以下功能：

* 显示时间树结构（年 / 月 / 日）
* 日期选择器联动表格高亮
* 点击表格项自动选中并回传
* 可折叠收起展开，过渡动画平滑
* 自动添加“全部”节点
* 自动禁用无数据的日期

---

## 🔧 Props 参数

| 参数名        | 类型               | 必填 | 默认值  | 说明             |
| ---------- | ---------------- | -- | ---- | -------------- |
| `timeData` | `TimeDataType[]` | ✅  | `[]` | 时间数据列表，见下方格式说明 |

#### TimeDataType 格式说明：

```ts
interface TimeDataType {
  id: string;                // 唯一 ID（年或日）
  year: number;              // 年份
  month: number;             // 月份
  day?: number;              // 日（如果是日节点才有）
  count: number;             // 数量（展示在名称后）
  parentId: string | null;   // 父节点 ID（例如某日对应的月份）
}
```

---

## 📄 Emits 事件

| 事件名      | 参数                  | 说明         |
| -------- | ------------------- | ---------- |
| `select` | `row: TimeDataType` | 当前选中的时间数据项 |

---

## ✨ 使用示例

```vue
<template>
  <div class="flex h-[500px] border">
    <!-- 左侧时间选择面板 -->
    <TimeSelect :timeData="timeList" @select="handleSelect" />

    <!-- 右侧显示选中项 -->
    <div class="flex-1 p-4">
      <p class="text-lg font-bold mb-2">你选中了：</p>
      <pre class="bg-gray-100 p-2 rounded">{{ selected }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import TimeSelect from '@/components/TimeSelect.vue';
import { ref } from 'vue';

const selected = ref<any>(null);

const timeList = [
  { id: '2024-01', year: 2024, month: 1, count: 30, parentId: null },
  { id: '2024-01-01', year: 2024, month: 1, day: 1, count: 5, parentId: '2024-01' },
  { id: '2024-01-02', year: 2024, month: 1, day: 2, count: 10, parentId: '2024-01' },
  { id: '2024-02', year: 2024, month: 2, count: 15, parentId: null },
  { id: '2024-02-03', year: 2024, month: 2, day: 3, count: 15, parentId: '2024-02' }
];

function handleSelect(row: any) {
  selected.value = row;
}
</script>
```

---

## 📅 组件内交互说明

| 功能           | 说明                         |
| ------------ | -------------------------- |
| 日期选择器选中日期    | 自动匹配表格中对应项并高亮              |
| 表格项点击        | 自动高亮并触发 `select` 事件        |
| 清空日期选择器      | 自动重置为“全部”项                 |
| 折叠按钮（左/ 右箭头） | 控制侧边栏宽度展开/收起（200px ↔ 30px） |
| 只允许选择存在的日期   | 不存在于 timeData 中的日期将被禁用     |

---

## 📝 Tips

* `timeData` 需要输入树状结构（月为父，日为子）
* 只有日节点会影响日期选择器的禁用日
* 使用 `watchEffect` 实时触发 timeData 更新

---

## 🗂 结合

这个组件适合用于数据查询前的日期预选择面板，或作为页面左侧栏。

如需要扩展多选 / 多级树 / 动态请求时间节点，可接着扩展设计。
