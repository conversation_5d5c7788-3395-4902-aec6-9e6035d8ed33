<!-- 组织选择器 -->
<template>
  <div class="role-selector w-full">
    <ElTreeSelect
      v-bind="$attrs"
      v-model="roleId"
      :data="data"
      :props="{
        label: 'name',
        disabled: (node: TreeNode) => node.nodeType !== 'role', // 非末级节点禁用
      }"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :default-expand-all="defaultExpandall"
      :multiple="multiple"
      collapse-tags-tooltip
      filterable
      :collapse-tags="true"
      :max-collapse-tags="2"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
      @remove-tag="handleRemoveClick"
      node-key="id"
    />
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue';

import { ElTreeSelect } from 'element-plus';
import _ from 'lodash';

import { useCommonStore } from '#/store';

interface TreeNode {
  nodeType: string;
  [key: string]: any;
}

interface Props {
  modelValue: null | string | string[];
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  defaultExpandall?: boolean;
  size?: 'default' | 'large' | 'small';
}

defineOptions({
  name: 'RoleSelector',
});
const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  placeholder: '请选择角色',
  disabled: false,
  clearable: false,
  multiple: true,
  defaultExpandall: true,
  size: 'default',
});

const emit = defineEmits(['update:modelValue', 'change']);

const commonStore = useCommonStore();

interface Tree {
  [key: string]: any;
}
const roleId = ref<string | string[]>([]);

const data = ref([]);
// 监听外部值变化
watch(
  () => props.modelValue,
  (val) => {
    roleId.value = val || [];
  },
  { immediate: true },
);

// 监听内部值变化
watch(
  () => roleId.value,
  (val) => {
    emit('update:modelValue', val);
  },
);

// 加载组织树数据
const loadOrgTree = async () => {
  try {
    data.value = commonStore.roleTreeData;
  } catch (error) {
    console.error('Failed to load organization tree:', error);
  }
};
function handleNodeClick(item: any) {
  if (item.nodeType === 'role') {
    const oldVal = _.cloneDeep(roleId.value);
    let type = '';
    let currentItem = '';
    setTimeout(() => {
      const newVal = roleId.value;
      const toDelete = _.difference(oldVal, newVal);
      const toAdd = _.difference(newVal, oldVal);
      if (toDelete.length > 0) {
        type = 'del';
        currentItem = toDelete[0] ? toDelete[0] : '';
      } else if (toAdd.length > 0) {
        type = 'add';
        currentItem = toAdd[0] ? toAdd[0] : '';
      }
      emit('change', currentItem, type);
    }, 0);
  }
}
function handleRemoveClick(item: any) {
  const type = 'del';
  setTimeout(() => {
    emit('change', item, type);
  }, 0);
}

onBeforeMount(() => {
  loadOrgTree();
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.name.includes(value);
};
</script>

<style lang="scss" scoped>
.org-selector {
  display: inline-block;
}
</style>
