<template>
  <div class="flex items-center gap-2">
    展开到
    <ElTooltip
      v-for="(icon, idx) in iconList"
      :key="idx"
      :content="icon.title"
      placement="top"
    >
      <IconifyIcon
        class="icon-btn"
        :class="[{ active: iconIdx === icon.idx }]"
        :icon="icon.name"
        @click="handleIconClick(icon.idx)"
      />
    </ElTooltip>
  </div>
</template>

<script setup lang="ts" name="TreeExpand">
import type { VxeGridInstance } from 'vxe-table';

import { ref, withDefaults } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElTooltip } from 'element-plus';

interface Props {
  gridRef: VxeGridInstance;
  parentId?: number | string;
}

const props = withDefaults(defineProps<Props>(), {
  parentId: '0',
} as Props);

const iconIdx = ref(0);
function handleIconClick(idx: number) {
  iconIdx.value = idx;
  switch (idx) {
    case 0: {
      props.gridRef && props.gridRef.setAllTreeExpand(true);

      break;
    }
    case 1: {
      // 展开一层
      props.gridRef && props.gridRef.clearTreeExpand();

      break;
    }
    case 2: {
      props.gridRef && props.gridRef.clearTreeExpand();
      props.gridRef &&
        props.gridRef.getTableData().tableData?.forEach((item) => {
          if (item.parentId === props.parentId) {
            props.gridRef && props.gridRef.setTreeExpand(item, true);
          }
        });

      break;
    }
    case 3: {
      if (props.gridRef) {
        const secondFloor = getDataByLevelWithPrevious(
          props.gridRef.getTableData().tableData,
          2,
        );
        secondFloor.forEach((item) => {
          props.gridRef && props.gridRef.setTreeExpand(item, true);
        });
      }

      break;
    }
    // No default
  }
}

function getDataByLevelWithPrevious(treeData: any[], level: number) {
  if (level < 1) return []; // 层级从1开始

  let currentLevelIds = new Set();
  let currentLevelData = [];
  const allLevelsData = [];

  // 初始化第一层数据
  const firstLevelData = treeData.filter(
    (item) => item.parentId === props.parentId,
  );
  if (level >= 1) {
    allLevelsData.push(...firstLevelData);
    currentLevelIds = new Set(firstLevelData.map((item) => item.id));
    currentLevelData = firstLevelData;
  }

  // 逐层遍历
  for (let i = 1; i < level; i++) {
    if (currentLevelIds.size === 0) break; // 如果当前层没有数据，直接退出

    // 获取下一层的节点
    const nextLevelData = treeData.filter((item) =>
      currentLevelIds.has(item.parentId),
    );
    allLevelsData.push(...nextLevelData);
    currentLevelIds = new Set(nextLevelData.map((item) => item.id));
    currentLevelData = nextLevelData;
  }

  return allLevelsData;
}

const iconList = ref([
  { idx: 0, name: 'ph:selection-all', title: '展开全部' },
  { idx: 1, name: 'icon-park-outline:one-key', title: '展开第一层' },
  { idx: 2, name: 'icon-park-outline:two-key', title: '展开第二层' },
  { idx: 3, name: 'icon-park-outline:three-key', title: '展开第三层' },
]);

function init(idx: number) {
  iconIdx.value = idx;
  handleIconClick(idx);
}

defineExpose({
  init,
});
</script>

<style scoped>
.icon-btn {
  width: 20px;
  height: 20px;
  outline: none;
  cursor: pointer;
  transition: color 0.3s;
}
.icon-btn.active {
  color: #006be6;
}
</style>
