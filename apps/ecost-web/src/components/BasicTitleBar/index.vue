<template>
  <div class="title-bar">
    <div class="bar"></div>
    <div class="text">{{ title }}</div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true,
  },
});
</script>

<style scoped>
.title-bar {
  display: flex;
  align-items: center;
}

.bar {
  width: 4px;
  height: 16px;
  background-color: #007bff; /* 蓝色 */
  border-radius: 2px;
  margin-right: 8px;
}

.text {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}
</style>
