<template>
  <div
    v-if="visible"
    class="fixed z-50 overflow-hidden rounded-lg border border-gray-300 bg-white shadow-lg"
    :style="dialogStyle"
  >
    <!-- 关闭按钮 -->
    <!-- 顶部拖拽区域 -->
    <div
      class="cursor-move border-b bg-gray-100 pb-1 pl-3 pr-3 pt-1 text-sm font-bold"
      @mousedown="startDrag"
    >
      AI提取
      <span
        class="float-right cursor-pointer hover:text-red-500"
        @click="dialogClose"
      >
        ✕
      </span>
    </div>

    <!-- 内容区域 -->
    <div class="box-border flex h-full w-full gap-4 p-4">
      <div class="flex w-[60%] items-center justify-center">
        <img
          class="h-[80%] max-h-[600px] object-contain"
          :src="imageUrl"
          draggable="false"
        />
      </div>
      <div class="w-[40%] overflow-auto bg-gray-50 p-4">
        <div>{{ imageText }}</div>
      </div>
    </div>

    <!-- 缩放角 -->
    <!-- 四个角 -->
    <div
      class="absolute left-0 top-0 h-4 w-4 cursor-nw-resize"
      @mousedown.stop="startResize($event, 'nw')"
    ></div>
    <div
      class="absolute right-0 top-0 h-4 w-4 cursor-ne-resize"
      @mousedown.stop="startResize($event, 'ne')"
    ></div>
    <div
      class="absolute bottom-0 left-0 h-4 w-4 cursor-sw-resize"
      @mousedown.stop="startResize($event, 'sw')"
    ></div>
    <div
      class="absolute bottom-0 right-0 h-4 w-4 cursor-se-resize"
      @mousedown.stop="startResize($event, 'se')"
    ></div>

    <!-- 四条边 -->

    <div
      class="absolute left-4 right-4 top-0 h-1 cursor-n-resize"
      @mousedown.stop="startResize($event, 'n')"
    ></div>

    <div
      class="absolute bottom-0 left-4 right-4 h-1 cursor-s-resize"
      @mousedown.stop="startResize($event, 's')"
    ></div>

    <div
      class="absolute bottom-4 left-0 top-4 w-1 cursor-w-resize"
      @mousedown.stop="startResize($event, 'w')"
    ></div>
    <div
      class="absolute bottom-4 right-0 top-4 w-1 cursor-e-resize"
      @mousedown.stop="startResize($event, 'e')"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, reactive } from 'vue';

defineProps<{
  imageText: string;
  imageUrl: string;
  visible: boolean;
}>();
const emit = defineEmits(['close']);

const position = reactive({ left: 300, top: 200 });
const size = reactive({ width: 800, height: 500 });

const dialogStyle = computed(
  () =>
    ({
      position: 'fixed',
      left: `${position.left}px`,
      top: `${position.top}px`,
      width: `${size.width}px`,
      height: `${size.height}px`,
      zIndex: 99,
      minWidth: '400px',
      minHeight: '200px',
    }) as const,
);

let dragging = false;
let resizing = false;
let offsetX = 0;
let offsetY = 0;

const startDrag = (e: MouseEvent) => {
  dragging = true;
  offsetX = e.clientX - position.left;
  offsetY = e.clientY - position.top;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopAction);
};

// const startResize = (e: MouseEvent) => {
//   resizing = true;
//   offsetX = e.clientX;
//   offsetY = e.clientY;
//   document.addEventListener('mousemove', onResize);
//   document.addEventListener('mouseup', stopAction);
// };
let resizeDir: null | string = null;
const startResize = (e: MouseEvent, direction: string) => {
  resizing = true;
  resizeDir = direction;
  offsetX = e.clientX;
  offsetY = e.clientY;
  document.addEventListener('mousemove', onResize);
  document.addEventListener('mouseup', stopAction);
};

const onDrag = (e: MouseEvent) => {
  if (!dragging) return;
  position.left = e.clientX - offsetX;
  position.top = e.clientY - offsetY;
};

// const onResize = (e: MouseEvent) => {
//   if (!resizing) return;
//   const dx = e.clientX - offsetX;
//   const dy = e.clientY - offsetY;
//   size.width += dx;
//   size.height += dy;
//   offsetX = e.clientX;
//   offsetY = e.clientY;
// };

const onResize = (e: MouseEvent) => {
  if (!resizing || !resizeDir) return;

  const dx = e.clientX - offsetX;
  const dy = e.clientY - offsetY;

  switch (resizeDir) {
    case 'e': {
      size.width += dx;
      break;
    }
    case 'n': {
      size.height -= dy;
      position.top += dy;
      break;
    }
    case 'ne': {
      size.width += dx;
      size.height -= dy;
      position.top += dy;
      break;
    }
    case 'nw': {
      size.width -= dx;
      size.height -= dy;
      position.left += dx;
      position.top += dy;
      break;
    }
    case 's': {
      size.height += dy;
      break;
    }
    case 'se': {
      size.width += dx;
      size.height += dy;
      break;
    }
    case 'sw': {
      size.width -= dx;
      size.height += dy;
      position.left += dx;
      break;
    }
    case 'w': {
      size.width -= dx;
      position.left += dx;
      break;
    }
  }

  // 最小尺寸控制
  size.width = Math.max(size.width, 400);
  size.height = Math.max(size.height, 200);

  offsetX = e.clientX;
  offsetY = e.clientY;
};

const dialogClose = () => {
  emit('close');
};

const stopAction = () => {
  dragging = false;
  resizing = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopAction);
};

onBeforeUnmount(() => stopAction());
</script>

<style scoped lang="scss"></style>
