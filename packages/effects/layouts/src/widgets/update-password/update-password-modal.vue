<template>
  <Modal
    :footer="false"
    :fullscreen-button="false"
    :title="$t('ui.widgets.updatePassword.title')"
  >
    <div
      class="mb-10 flex w-full flex-col items-center px-10"
      @keydown.enter.prevent="handleSubmit"
    >
      <div class="w-full">
        <div class="ml-2 flex w-full flex-col items-center"></div>
        <Form />
        <VbenButton class="mt-1 w-full" @click="handleSubmit">
          {{ $t('ui.widgets.updatePassword.screenButton') }}
        </VbenButton>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { computed, reactive } from 'vue';

import { $t } from '@vben/locales';

import { useVbenForm, z } from '@vben-core/form-ui';
import { useVbenModal } from '@vben-core/popup-ui';
import { VbenButton } from '@vben-core/shadcn-ui';

import message from 'element-plus/es/components/message/index.mjs';

// interface Props {
//   avatar?: string;
//   text?: string;
// }

defineOptions({
  name: 'UpdatePasswordModal',
});

// withDefaults(defineProps<Props>(), {
//   avatar: '',
//   text: '',
// });

const emit = defineEmits<{
  submit: [Recordable<any>];
}>();

const [Form, { resetForm, validate, getValues }] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
    },
    schema: computed(() => [
      {
        component: 'VbenInputPassword' as const,
        componentProps: {
          placeholder: $t('ui.widgets.updatePassword.oldPasswordPlaceholder'),
        },
        fieldName: 'oldPassword',
        formFieldProps: { validateOnBlur: false },
        // hideLabel: false,
        label: $t('authentication.password'),
        rules: z.string().min(1, {
          message: $t('ui.widgets.updatePassword.oldPasswordPlaceholder'),
        }),
      },
      {
        component: 'VbenInputPassword' as const,
        componentProps: {
          placeholder: $t('ui.widgets.updatePassword.newPasswordPlaceholder'),
        },
        fieldName: 'newPassword',
        // hideLabel: false,
        formFieldProps: { validateOnBlur: false },
        label: $t('authentication.newPassword'),
        rules: z.string().min(1, {
          message: $t('ui.widgets.updatePassword.newPasswordPlaceholder'),
        }),
      },
      {
        component: 'VbenInputPassword' as const,
        componentProps: {
          placeholder: $t('ui.widgets.updatePassword.checkPasswordPlaceholder'),
        },
        // hideLabel: false,
        fieldName: 'checkPassword',
        formFieldProps: { validateOnBlur: false },
        label: $t('authentication.checkPassword'),
        rules: z.string().min(1, {
          message: $t('ui.widgets.updatePassword.checkPasswordPlaceholder'),
        }),
      },
    ]),
    showDefaultActions: false,
  }),
);

const [Modal] = useVbenModal({
  onConfirm() {
    handleSubmit();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      resetForm();
    }
  },
});

async function handleSubmit() {
  const { valid } = await validate();
  const values = await getValues();
  if (valid) {
    if (values?.newPassword !== values?.checkPassword) {
      message.error($t('ui.widgets.updatePassword.checkPasswordTip'));
      return;
    }
    if (values?.oldPassword === values?.newPassword) {
      message.error($t('ui.widgets.updatePassword.checkTwoPasswordTip'));
      return;
    }
    emit('submit', {
      oldPassword: values?.oldPassword,
      newPassword: values?.newPassword,
    });
  }
}
</script>
